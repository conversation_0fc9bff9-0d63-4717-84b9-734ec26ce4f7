import { io, Socket } from 'socket.io-client'
import { TemplateComponent } from '../types/templateEditor'

interface CollaborationUser {
  id: string
  name: string
  email: string
  color: string
  cursor?: { x: number; y: number }
  selection?: string[]
  lastSeen: number
}

interface CollaborationEvent {
  type: 'component_add' | 'component_update' | 'component_delete' | 'cursor_move' | 'selection_change' | 'user_join' | 'user_leave'
  userId: string
  timestamp: number
  data: any
}

interface ConflictResolution {
  strategy: 'last_write_wins' | 'merge' | 'manual'
  resolver?: (local: any, remote: any) => any
}

class CollaborationService {
  private socket: Socket | null = null
  private currentUser: CollaborationUser | null = null
  private connectedUsers = new Map<string, CollaborationUser>()
  private templateId: string | null = null
  private isConnected = false
  private eventHandlers = new Map<string, Function[]>()
  private conflictResolution: ConflictResolution = { strategy: 'last_write_wins' }
  private operationQueue: CollaborationEvent[] = []
  private isProcessingQueue = false

  constructor() {
    this.setupEventHandlers()
  }

  // Initialize collaboration for a template
  async connect(templateId: string, user: Omit<CollaborationUser, 'lastSeen'>): Promise<void> {
    if (this.socket?.connected) {
      await this.disconnect()
    }

    this.templateId = templateId
    this.currentUser = { ...user, lastSeen: Date.now() }

    try {
      this.socket = io(process.env.REACT_APP_WS_URL || 'ws://localhost:8001', {
        auth: {
          token: localStorage.getItem('auth_token'),
          templateId,
          user: this.currentUser
        },
        transports: ['websocket', 'polling']
      })

      await this.setupSocketListeners()
      
      return new Promise((resolve, reject) => {
        this.socket!.on('connect', () => {
          this.isConnected = true
          console.log('Connected to collaboration server')
          resolve()
        })

        this.socket!.on('connect_error', (error) => {
          console.error('Collaboration connection error:', error)
          reject(error)
        })

        setTimeout(() => reject(new Error('Connection timeout')), 10000)
      })
    } catch (error) {
      console.error('Failed to connect to collaboration server:', error)
      throw error
    }
  }

  // Disconnect from collaboration
  async disconnect(): Promise<void> {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
    
    this.isConnected = false
    this.connectedUsers.clear()
    this.templateId = null
    this.currentUser = null
    this.operationQueue = []
  }

  // Send component operation
  sendOperation(type: CollaborationEvent['type'], data: any): void {
    if (!this.isConnected || !this.socket || !this.currentUser) {
      console.warn('Cannot send operation: not connected')
      return
    }

    const event: CollaborationEvent = {
      type,
      userId: this.currentUser.id,
      timestamp: Date.now(),
      data
    }

    this.socket.emit('operation', event)
  }

  // Component operations
  addComponent(component: TemplateComponent): void {
    this.sendOperation('component_add', { component })
  }

  updateComponent(componentId: string, updates: Partial<TemplateComponent>): void {
    this.sendOperation('component_update', { componentId, updates })
  }

  deleteComponent(componentId: string): void {
    this.sendOperation('component_delete', { componentId })
  }

  // Cursor and selection tracking
  updateCursor(x: number, y: number): void {
    if (!this.currentUser) return
    
    this.currentUser.cursor = { x, y }
    this.sendOperation('cursor_move', { x, y })
  }

  updateSelection(componentIds: string[]): void {
    if (!this.currentUser) return
    
    this.currentUser.selection = componentIds
    this.sendOperation('selection_change', { componentIds })
  }

  // Event handling
  on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, [])
    }
    this.eventHandlers.get(event)!.push(handler)
  }

  off(event: string, handler: Function): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  private emit(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.forEach(handler => handler(data))
    }
  }

  // Socket event setup
  private async setupSocketListeners(): Promise<void> {
    if (!this.socket) return

    this.socket.on('user_joined', (user: CollaborationUser) => {
      this.connectedUsers.set(user.id, user)
      this.emit('user_joined', user)
    })

    this.socket.on('user_left', (userId: string) => {
      this.connectedUsers.delete(userId)
      this.emit('user_left', userId)
    })

    this.socket.on('users_list', (users: CollaborationUser[]) => {
      this.connectedUsers.clear()
      users.forEach(user => this.connectedUsers.set(user.id, user))
      this.emit('users_updated', Array.from(this.connectedUsers.values()))
    })

    this.socket.on('operation', (event: CollaborationEvent) => {
      this.handleRemoteOperation(event)
    })

    this.socket.on('conflict', (conflictData: any) => {
      this.handleConflict(conflictData)
    })

    this.socket.on('disconnect', () => {
      this.isConnected = false
      this.emit('disconnected', null)
    })

    this.socket.on('reconnect', () => {
      this.isConnected = true
      this.emit('reconnected', null)
      this.processOperationQueue()
    })
  }

  // Handle remote operations
  private async handleRemoteOperation(event: CollaborationEvent): Promise<void> {
    // Skip operations from current user
    if (event.userId === this.currentUser?.id) return

    try {
      switch (event.type) {
        case 'component_add':
          this.emit('remote_component_add', event.data)
          break
        case 'component_update':
          this.emit('remote_component_update', event.data)
          break
        case 'component_delete':
          this.emit('remote_component_delete', event.data)
          break
        case 'cursor_move':
          this.updateUserCursor(event.userId, event.data)
          break
        case 'selection_change':
          this.updateUserSelection(event.userId, event.data)
          break
      }
    } catch (error) {
      console.error('Error handling remote operation:', error)
    }
  }

  // Conflict resolution
  private handleConflict(conflictData: any): void {
    const { local, remote, componentId } = conflictData

    switch (this.conflictResolution.strategy) {
      case 'last_write_wins':
        // Remote operation wins by default
        this.emit('conflict_resolved', { resolution: 'remote', componentId, data: remote })
        break
      
      case 'merge':
        if (this.conflictResolution.resolver) {
          const merged = this.conflictResolution.resolver(local, remote)
          this.emit('conflict_resolved', { resolution: 'merged', componentId, data: merged })
        } else {
          this.emit('conflict_manual', { local, remote, componentId })
        }
        break
      
      case 'manual':
        this.emit('conflict_manual', { local, remote, componentId })
        break
    }
  }

  // User tracking
  private updateUserCursor(userId: string, cursor: { x: number; y: number }): void {
    const user = this.connectedUsers.get(userId)
    if (user) {
      user.cursor = cursor
      user.lastSeen = Date.now()
      this.emit('user_cursor_updated', { userId, cursor })
    }
  }

  private updateUserSelection(userId: string, selection: { componentIds: string[] }): void {
    const user = this.connectedUsers.get(userId)
    if (user) {
      user.selection = selection.componentIds
      user.lastSeen = Date.now()
      this.emit('user_selection_updated', { userId, selection: selection.componentIds })
    }
  }

  // Operation queue for offline support
  private queueOperation(event: CollaborationEvent): void {
    this.operationQueue.push(event)
    
    // Limit queue size
    if (this.operationQueue.length > 100) {
      this.operationQueue = this.operationQueue.slice(-50)
    }
  }

  private async processOperationQueue(): Promise<void> {
    if (this.isProcessingQueue || this.operationQueue.length === 0) return
    
    this.isProcessingQueue = true
    
    try {
      while (this.operationQueue.length > 0 && this.isConnected) {
        const operation = this.operationQueue.shift()!
        this.socket?.emit('operation', operation)
        
        // Small delay to prevent overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 10))
      }
    } catch (error) {
      console.error('Error processing operation queue:', error)
    } finally {
      this.isProcessingQueue = false
    }
  }

  // Utility methods
  getConnectedUsers(): CollaborationUser[] {
    return Array.from(this.connectedUsers.values())
  }

  getCurrentUser(): CollaborationUser | null {
    return this.currentUser
  }

  isUserConnected(): boolean {
    return this.isConnected
  }

  setConflictResolution(resolution: ConflictResolution): void {
    this.conflictResolution = resolution
  }

  // Cleanup
  private setupEventHandlers(): void {
    // Handle page unload
    window.addEventListener('beforeunload', () => {
      this.disconnect()
    })

    // Handle visibility change
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // User switched tabs/minimized
        this.sendOperation('user_leave', { reason: 'tab_hidden' })
      } else {
        // User returned
        this.sendOperation('user_join', { reason: 'tab_visible' })
      }
    })
  }
}

// Create singleton instance
export const collaborationService = new CollaborationService()

// User color generator
export const generateUserColor = (userId: string): string => {
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ]
  
  let hash = 0
  for (let i = 0; i < userId.length; i++) {
    hash = userId.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  return colors[Math.abs(hash) % colors.length]
}

export { CollaborationService }
export type { CollaborationUser, CollaborationEvent, ConflictResolution }
