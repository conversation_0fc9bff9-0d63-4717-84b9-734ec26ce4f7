import React, { useState } from 'react'
import { X, Search, Filter, Star, Eye, Copy } from 'lucide-react'

interface TemplateGalleryItem {
  id: string
  name: string
  description: string
  category: string
  thumbnail: string
  isPremium: boolean
  rating: number
  usageCount: number
  components: any[]
  settings: any
}

interface MailchimpTemplateGalleryProps {
  isOpen: boolean
  onClose: () => void
  onSelectTemplate: (template: TemplateGalleryItem) => void
}

const TEMPLATE_CATEGORIES = [
  'All Templates',
  'Newsletter',
  'Promotional',
  'Welcome',
  'Product Launch',
  'Event',
  'Holiday',
  'E-commerce',
  'B2B',
  'Non-profit'
]

const SAMPLE_TEMPLATES: TemplateGalleryItem[] = [
  {
    id: 'newsletter-1',
    name: 'Modern Newsletter',
    description: 'Clean and professional newsletter template with image header',
    category: 'Newsletter',
    thumbnail: 'https://via.placeholder.com/300x400/f0f0f0/666666?text=Newsletter',
    isPremium: false,
    rating: 4.8,
    usageCount: 1250,
    components: [
      {
        id: 'header-1',
        type: 'image',
        content: { src: 'https://via.placeholder.com/600x200/007C89/ffffff?text=Newsletter+Header', alt: 'Newsletter Header' },
        styles: { width: '100%', marginBottom: '20px' }
      },
      {
        id: 'heading-1',
        type: 'heading',
        content: { text: 'Welcome to Our Newsletter', level: 'h1' },
        styles: { fontSize: '32px', textAlign: 'center', marginBottom: '20px', color: '#333333' }
      },
      {
        id: 'text-1',
        type: 'text',
        content: { text: 'Stay updated with our latest news, insights, and exclusive offers. We\'re excited to share what\'s new with you!' },
        styles: { fontSize: '16px', lineHeight: '1.6', marginBottom: '30px', textAlign: 'center' }
      }
    ],
    settings: { backgroundColor: '#ffffff', fontFamily: 'Arial, sans-serif', width: 600 }
  },
  {
    id: 'promo-1',
    name: 'Sale Announcement',
    description: 'Eye-catching promotional template for sales and special offers',
    category: 'Promotional',
    thumbnail: 'https://via.placeholder.com/300x400/FF6B6B/ffffff?text=Sale',
    isPremium: false,
    rating: 4.6,
    usageCount: 890,
    components: [
      {
        id: 'heading-1',
        type: 'heading',
        content: { text: '🎉 HUGE SALE! 50% OFF', level: 'h1' },
        styles: { fontSize: '36px', textAlign: 'center', marginBottom: '20px', color: '#FF6B6B', fontWeight: 'bold' }
      },
      {
        id: 'text-1',
        type: 'text',
        content: { text: 'Don\'t miss out on our biggest sale of the year! Limited time offer.' },
        styles: { fontSize: '18px', textAlign: 'center', marginBottom: '30px' }
      },
      {
        id: 'button-1',
        type: 'button',
        content: { text: 'Shop Now', href: '#' },
        styles: { backgroundColor: '#FF6B6B', color: '#ffffff', padding: '15px 30px', fontSize: '18px', fontWeight: 'bold' }
      }
    ],
    settings: { backgroundColor: '#ffffff', fontFamily: 'Arial, sans-serif', width: 600 }
  },
  {
    id: 'welcome-1',
    name: 'Welcome Series',
    description: 'Warm welcome template for new subscribers',
    category: 'Welcome',
    thumbnail: 'https://via.placeholder.com/300x400/4ECDC4/ffffff?text=Welcome',
    isPremium: true,
    rating: 4.9,
    usageCount: 2100,
    components: [
      {
        id: 'heading-1',
        type: 'heading',
        content: { text: 'Welcome to Our Community! 👋', level: 'h1' },
        styles: { fontSize: '28px', textAlign: 'center', marginBottom: '20px', color: '#4ECDC4' }
      },
      {
        id: 'text-1',
        type: 'text',
        content: { text: 'We\'re thrilled to have you join us! Here\'s what you can expect from our emails...' },
        styles: { fontSize: '16px', lineHeight: '1.6', marginBottom: '20px' }
      }
    ],
    settings: { backgroundColor: '#f8f9fa', fontFamily: 'Arial, sans-serif', width: 600 }
  },
  {
    id: 'product-1',
    name: 'Product Showcase',
    description: 'Perfect for highlighting new products or features',
    category: 'Product Launch',
    thumbnail: 'https://via.placeholder.com/300x400/9B59B6/ffffff?text=Product',
    isPremium: false,
    rating: 4.7,
    usageCount: 650,
    components: [
      {
        id: 'product-1',
        type: 'product',
        content: {
          name: 'Amazing Product',
          price: '$99.99',
          image: 'https://via.placeholder.com/200x200/9B59B6/ffffff?text=Product',
          description: 'Discover our latest innovation that will change the way you work.',
          buttonText: 'Learn More',
          buttonLink: '#'
        },
        styles: { textAlign: 'center', padding: '30px', backgroundColor: '#ffffff', borderRadius: '8px' }
      }
    ],
    settings: { backgroundColor: '#f5f5f5', fontFamily: 'Arial, sans-serif', width: 600 }
  },
  {
    id: 'event-1',
    name: 'Event Invitation',
    description: 'Professional template for event announcements and invitations',
    category: 'Event',
    thumbnail: 'https://via.placeholder.com/300x400/E67E22/ffffff?text=Event',
    isPremium: true,
    rating: 4.5,
    usageCount: 420,
    components: [
      {
        id: 'heading-1',
        type: 'heading',
        content: { text: 'You\'re Invited! 🎪', level: 'h1' },
        styles: { fontSize: '32px', textAlign: 'center', marginBottom: '20px', color: '#E67E22' }
      },
      {
        id: 'text-1',
        type: 'text',
        content: { text: 'Join us for an exclusive event you won\'t want to miss!' },
        styles: { fontSize: '18px', textAlign: 'center', marginBottom: '30px' }
      }
    ],
    settings: { backgroundColor: '#ffffff', fontFamily: 'Georgia, serif', width: 600 }
  }
]

export const MailchimpTemplateGallery: React.FC<MailchimpTemplateGalleryProps> = ({
  isOpen,
  onClose,
  onSelectTemplate
}) => {
  const [selectedCategory, setSelectedCategory] = useState('All Templates')
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState('popular')

  if (!isOpen) return null

  const filteredTemplates = SAMPLE_TEMPLATES.filter(template => {
    const matchesCategory = selectedCategory === 'All Templates' || template.category === selectedCategory
    const matchesSearch = searchTerm === '' || 
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase())
    
    return matchesCategory && matchesSearch
  }).sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.usageCount - a.usageCount
      case 'rating':
        return b.rating - a.rating
      case 'name':
        return a.name.localeCompare(b.name)
      default:
        return 0
    }
  })

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Choose a Template</h2>
            <p className="text-gray-600 mt-1">Start with a professionally designed template</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Filters */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="flex items-center space-x-4">
              {/* Category Filter */}
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {TEMPLATE_CATEGORIES.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>

              {/* Sort */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="popular">Most Popular</option>
                <option value="rating">Highest Rated</option>
                <option value="name">Name A-Z</option>
              </select>
            </div>
          </div>
        </div>

        {/* Template Grid */}
        <div className="flex-1 overflow-y-auto p-6">
          {filteredTemplates.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">📧</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredTemplates.map(template => (
                <div
                  key={template.id}
                  className="group bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200 cursor-pointer"
                  onClick={() => onSelectTemplate(template)}
                >
                  {/* Template Preview */}
                  <div className="relative aspect-[3/4] bg-gray-100">
                    <img
                      src={template.thumbnail}
                      alt={template.name}
                      className="w-full h-full object-cover"
                    />
                    
                    {/* Premium Badge */}
                    {template.isPremium && (
                      <div className="absolute top-2 right-2 bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-medium">
                        <Star className="w-3 h-3 inline mr-1" />
                        Premium
                      </div>
                    )}

                    {/* Hover Overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                        <button className="bg-white text-gray-900 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                          <Eye className="w-4 h-4 inline mr-2" />
                          Preview
                        </button>
                        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                          <Copy className="w-4 h-4 inline mr-2" />
                          Use Template
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Template Info */}
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-1 truncate">
                      {template.name}
                    </h3>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {template.description}
                    </p>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center space-x-2">
                        <div className="flex items-center">
                          <Star className="w-3 h-3 text-yellow-400 fill-current mr-1" />
                          <span>{template.rating}</span>
                        </div>
                        <span>•</span>
                        <span>{template.usageCount.toLocaleString()} uses</span>
                      </div>
                      <span className="bg-gray-100 px-2 py-1 rounded text-xs">
                        {template.category}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''} found
            </p>
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors"
            >
              Start from Scratch
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MailchimpTemplateGallery
