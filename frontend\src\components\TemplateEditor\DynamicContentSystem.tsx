import React, { useState, useCallback, useMemo } from 'react'
import { Code, Database, User, Calendar, ShoppingCart, Zap, Settings } from 'lucide-react'

interface DynamicContent {
  id: string
  type: 'personalization' | 'conditional' | 'loop' | 'api_data' | 'calculation' | 'date_time'
  name: string
  description: string
  config: DynamicContentConfig
  preview?: string
}

interface DynamicContentConfig {
  personalization?: {
    field: string
    fallback?: string
    format?: 'text' | 'title_case' | 'upper_case' | 'lower_case'
  }
  conditional?: {
    condition: string
    trueContent: string
    falseContent: string
    operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'exists'
    value: any
  }
  loop?: {
    dataSource: string
    template: string
    limit?: number
    emptyMessage?: string
  }
  apiData?: {
    endpoint: string
    method: 'GET' | 'POST'
    headers?: Record<string, string>
    params?: Record<string, any>
    transform?: string
    cache?: boolean
    cacheDuration?: number
  }
  calculation?: {
    formula: string
    variables: Record<string, string>
    format?: 'number' | 'currency' | 'percentage'
  }
  dateTime?: {
    format: string
    timezone?: string
    relative?: boolean
    field?: string
  }
}

interface DynamicContentSystemProps {
  onContentAdd: (content: DynamicContent) => void
  availableFields: string[]
  userContext?: Record<string, any>
}

const DYNAMIC_CONTENT_TYPES = [
  {
    type: 'personalization' as const,
    name: 'Personalization',
    description: 'Insert user-specific data like name, email, etc.',
    icon: User,
    color: 'blue'
  },
  {
    type: 'conditional' as const,
    name: 'Conditional Content',
    description: 'Show different content based on conditions',
    icon: Code,
    color: 'green'
  },
  {
    type: 'loop' as const,
    name: 'Dynamic Lists',
    description: 'Repeat content for each item in a list',
    icon: Database,
    color: 'purple'
  },
  {
    type: 'api_data' as const,
    name: 'API Data',
    description: 'Fetch and display real-time data',
    icon: Zap,
    color: 'yellow'
  },
  {
    type: 'calculation' as const,
    name: 'Calculations',
    description: 'Perform calculations and display results',
    icon: Calculator,
    color: 'red'
  },
  {
    type: 'date_time' as const,
    name: 'Date & Time',
    description: 'Display formatted dates and times',
    icon: Calendar,
    color: 'indigo'
  }
]

const Calculator = ShoppingCart // Using ShoppingCart as Calculator icon

export const DynamicContentSystem: React.FC<DynamicContentSystemProps> = ({
  onContentAdd,
  availableFields,
  userContext = {}
}) => {
  const [selectedType, setSelectedType] = useState<DynamicContent['type'] | null>(null)
  const [config, setConfig] = useState<DynamicContentConfig>({})
  const [preview, setPreview] = useState('')

  const generatePreview = useCallback((type: DynamicContent['type'], config: DynamicContentConfig) => {
    switch (type) {
      case 'personalization':
        const field = config.personalization?.field || 'name'
        const fallback = config.personalization?.fallback || 'there'
        const value = userContext[field] || fallback
        return `Hello ${value}!`
      
      case 'conditional':
        const condition = config.conditional?.condition || 'user.type'
        const trueContent = config.conditional?.trueContent || 'Premium content'
        const falseContent = config.conditional?.falseContent || 'Standard content'
        return `${condition} ? "${trueContent}" : "${falseContent}"`
      
      case 'loop':
        const template = config.loop?.template || 'Item: {{item.name}}'
        return `Repeat: ${template}`
      
      case 'api_data':
        const endpoint = config.apiData?.endpoint || '/api/data'
        return `Data from ${endpoint}`
      
      case 'calculation':
        const formula = config.calculation?.formula || 'price * quantity'
        return `Calculate: ${formula}`
      
      case 'date_time':
        const format = config.dateTime?.format || 'YYYY-MM-DD'
        return new Date().toLocaleDateString()
      
      default:
        return 'Dynamic content preview'
    }
  }, [userContext])

  const handleConfigChange = useCallback((newConfig: Partial<DynamicContentConfig>) => {
    const updatedConfig = { ...config, ...newConfig }
    setConfig(updatedConfig)
    
    if (selectedType) {
      setPreview(generatePreview(selectedType, updatedConfig))
    }
  }, [config, selectedType, generatePreview])

  const handleAddContent = useCallback(() => {
    if (!selectedType) return

    const content: DynamicContent = {
      id: `dynamic_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: selectedType,
      name: `${DYNAMIC_CONTENT_TYPES.find(t => t.type === selectedType)?.name} Block`,
      description: 'Dynamic content block',
      config,
      preview
    }

    onContentAdd(content)
    setSelectedType(null)
    setConfig({})
    setPreview('')
  }, [selectedType, config, preview, onContentAdd])

  const renderConfigForm = () => {
    if (!selectedType) return null

    switch (selectedType) {
      case 'personalization':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Field
              </label>
              <select
                value={config.personalization?.field || ''}
                onChange={(e) => handleConfigChange({
                  personalization: { ...config.personalization, field: e.target.value }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="">Select field</option>
                {availableFields.map(field => (
                  <option key={field} value={field}>{field}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Fallback Value
              </label>
              <input
                type="text"
                value={config.personalization?.fallback || ''}
                onChange={(e) => handleConfigChange({
                  personalization: { ...config.personalization, fallback: e.target.value }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="Default value if field is empty"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Format
              </label>
              <select
                value={config.personalization?.format || 'text'}
                onChange={(e) => handleConfigChange({
                  personalization: { ...config.personalization, format: e.target.value as any }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="text">Normal Text</option>
                <option value="title_case">Title Case</option>
                <option value="upper_case">UPPER CASE</option>
                <option value="lower_case">lower case</option>
              </select>
            </div>
          </div>
        )

      case 'conditional':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Condition Field
              </label>
              <input
                type="text"
                value={config.conditional?.condition || ''}
                onChange={(e) => handleConfigChange({
                  conditional: { ...config.conditional, condition: e.target.value }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="e.g., user.subscription_type"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Operator
              </label>
              <select
                value={config.conditional?.operator || 'equals'}
                onChange={(e) => handleConfigChange({
                  conditional: { ...config.conditional, operator: e.target.value as any }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="equals">Equals</option>
                <option value="not_equals">Not Equals</option>
                <option value="contains">Contains</option>
                <option value="greater_than">Greater Than</option>
                <option value="less_than">Less Than</option>
                <option value="exists">Exists</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Value
              </label>
              <input
                type="text"
                value={config.conditional?.value || ''}
                onChange={(e) => handleConfigChange({
                  conditional: { ...config.conditional, value: e.target.value }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="Value to compare against"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Content if True
              </label>
              <textarea
                value={config.conditional?.trueContent || ''}
                onChange={(e) => handleConfigChange({
                  conditional: { ...config.conditional, trueContent: e.target.value }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                rows={3}
                placeholder="Content to show when condition is true"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Content if False
              </label>
              <textarea
                value={config.conditional?.falseContent || ''}
                onChange={(e) => handleConfigChange({
                  conditional: { ...config.conditional, falseContent: e.target.value }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                rows={3}
                placeholder="Content to show when condition is false"
              />
            </div>
          </div>
        )

      case 'loop':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Data Source
              </label>
              <input
                type="text"
                value={config.loop?.dataSource || ''}
                onChange={(e) => handleConfigChange({
                  loop: { ...config.loop, dataSource: e.target.value }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="e.g., user.orders, products"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Template
              </label>
              <textarea
                value={config.loop?.template || ''}
                onChange={(e) => handleConfigChange({
                  loop: { ...config.loop, template: e.target.value }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                rows={4}
                placeholder="Template for each item (use {{item.property}} for values)"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Limit (optional)
              </label>
              <input
                type="number"
                value={config.loop?.limit || ''}
                onChange={(e) => handleConfigChange({
                  loop: { ...config.loop, limit: parseInt(e.target.value) || undefined }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="Maximum number of items to show"
              />
            </div>
          </div>
        )

      case 'api_data':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                API Endpoint
              </label>
              <input
                type="text"
                value={config.apiData?.endpoint || ''}
                onChange={(e) => handleConfigChange({
                  apiData: { ...config.apiData, endpoint: e.target.value }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="e.g., /api/user/stats"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Method
              </label>
              <select
                value={config.apiData?.method || 'GET'}
                onChange={(e) => handleConfigChange({
                  apiData: { ...config.apiData, method: e.target.value as 'GET' | 'POST' }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="GET">GET</option>
                <option value="POST">POST</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Transform (optional)
              </label>
              <textarea
                value={config.apiData?.transform || ''}
                onChange={(e) => handleConfigChange({
                  apiData: { ...config.apiData, transform: e.target.value }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                rows={3}
                placeholder="JavaScript to transform the API response"
              />
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={config.apiData?.cache || false}
                onChange={(e) => handleConfigChange({
                  apiData: { ...config.apiData, cache: e.target.checked }
                })}
                className="mr-2"
              />
              <label className="text-sm text-gray-700">Cache response</label>
            </div>
          </div>
        )

      case 'calculation':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Formula
              </label>
              <input
                type="text"
                value={config.calculation?.formula || ''}
                onChange={(e) => handleConfigChange({
                  calculation: { ...config.calculation, formula: e.target.value }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="e.g., price * quantity * (1 + tax_rate)"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Format
              </label>
              <select
                value={config.calculation?.format || 'number'}
                onChange={(e) => handleConfigChange({
                  calculation: { ...config.calculation, format: e.target.value as any }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="number">Number</option>
                <option value="currency">Currency</option>
                <option value="percentage">Percentage</option>
              </select>
            </div>
          </div>
        )

      case 'date_time':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Format
              </label>
              <select
                value={config.dateTime?.format || 'YYYY-MM-DD'}
                onChange={(e) => handleConfigChange({
                  dateTime: { ...config.dateTime, format: e.target.value }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="YYYY-MM-DD">2024-01-15</option>
                <option value="MM/DD/YYYY">01/15/2024</option>
                <option value="DD/MM/YYYY">15/01/2024</option>
                <option value="MMMM DD, YYYY">January 15, 2024</option>
                <option value="dddd, MMMM DD, YYYY">Monday, January 15, 2024</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date Field (optional)
              </label>
              <input
                type="text"
                value={config.dateTime?.field || ''}
                onChange={(e) => handleConfigChange({
                  dateTime: { ...config.dateTime, field: e.target.value }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="Leave empty for current date"
              />
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={config.dateTime?.relative || false}
                onChange={(e) => handleConfigChange({
                  dateTime: { ...config.dateTime, relative: e.target.checked }
                })}
                className="mr-2"
              />
              <label className="text-sm text-gray-700">Show relative time (e.g., "2 days ago")</label>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="p-4 border-t border-gray-200">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Dynamic Content</h3>
      
      {!selectedType ? (
        <div className="grid grid-cols-2 gap-3">
          {DYNAMIC_CONTENT_TYPES.map((type) => {
            const Icon = type.icon
            return (
              <button
                key={type.type}
                onClick={() => setSelectedType(type.type)}
                className="p-3 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors text-left"
              >
                <div className="flex items-center space-x-2 mb-2">
                  <Icon className={`w-5 h-5 text-${type.color}-500`} />
                  <span className="font-medium text-gray-900">{type.name}</span>
                </div>
                <p className="text-sm text-gray-600">{type.description}</p>
              </button>
            )
          })}
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900">
              Configure {DYNAMIC_CONTENT_TYPES.find(t => t.type === selectedType)?.name}
            </h4>
            <button
              onClick={() => setSelectedType(null)}
              className="text-gray-500 hover:text-gray-700"
            >
              ×
            </button>
          </div>
          
          {renderConfigForm()}
          
          {preview && (
            <div className="p-3 bg-gray-50 rounded-md">
              <p className="text-sm font-medium text-gray-700 mb-1">Preview:</p>
              <p className="text-sm text-gray-900">{preview}</p>
            </div>
          )}
          
          <div className="flex space-x-2">
            <button
              onClick={handleAddContent}
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              Add Content
            </button>
            <button
              onClick={() => setSelectedType(null)}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default DynamicContentSystem
