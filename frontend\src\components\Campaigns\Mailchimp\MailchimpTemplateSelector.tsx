import React, { useState, useEffect } from 'react'
import { 
  FileText, Plus, Search, Eye, Edit, Copy, 
  Star, Clock, Palette, Code, Image, Layout
} from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { api } from '../../../services/api'

interface Template {
  id: number
  name: string
  description?: string
  thumbnail?: string
  category: string
  isGallery?: boolean
  created_at: string
  updated_at: string
}

interface CampaignData {
  template: {
    id?: number
    type: 'existing' | 'new' | 'gallery'
    content?: any
  }
}

interface MailchimpTemplateSelectorProps {
  campaignData: CampaignData
  onUpdate: (updates: Partial<CampaignData>) => void
  onNext: () => void
  onPrevious: () => void
  canProceed: boolean
  isLastStep: boolean
  loading: boolean
}

export const MailchimpTemplateSelector: React.FC<MailchimpTemplateSelectorProps> = ({
  campaignData,
  onUpdate,
  canProceed,
  loading
}) => {
  const navigate = useNavigate()
  const [templates, setTemplates] = useState<Template[]>([])
  const [galleryTemplates, setGalleryTemplates] = useState<Template[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [activeTab, setActiveTab] = useState<'my-templates' | 'gallery' | 'create-new'>('my-templates')
  const [loadingTemplates, setLoadingTemplates] = useState(true)

  const categories = [
    { id: 'all', name: 'All Templates' },
    { id: 'newsletter', name: 'Newsletter' },
    { id: 'promotional', name: 'Promotional' },
    { id: 'welcome', name: 'Welcome' },
    { id: 'product', name: 'Product' },
    { id: 'event', name: 'Event' }
  ]

  const createOptions = [
    {
      id: 'blank',
      name: 'Start from Scratch',
      description: 'Begin with a blank template and design from scratch',
      icon: Palette,
      action: () => createNewTemplate('blank')
    },
    {
      id: 'html',
      name: 'Code Your Own',
      description: 'Import your own HTML or start with code',
      icon: Code,
      action: () => createNewTemplate('html')
    },
    {
      id: 'import',
      name: 'Import Template',
      description: 'Upload an existing HTML template',
      icon: FileText,
      action: () => createNewTemplate('import')
    }
  ]

  useEffect(() => {
    loadTemplates()
    loadGalleryTemplates()
  }, [])

  const loadTemplates = async () => {
    try {
      setLoadingTemplates(true)
      const response = await api.get<any[]>('/templates')
      setTemplates((response || []).map((template: any) => ({
        id: template.id || 0,
        name: template.name,
        description: template.description || '',
        thumbnail: template.thumbnail,
        category: 'custom',
        created_at: template.created_at || '',
        updated_at: template.updated_at || ''
      })))
    } catch (error) {
      console.error('Failed to load templates:', error)
    } finally {
      setLoadingTemplates(false)
    }
  }

  const loadGalleryTemplates = async () => {
    // Mock gallery templates - in real app, this would come from API
    const mockGalleryTemplates: Template[] = [
      {
        id: 1001,
        name: 'Modern Newsletter',
        description: 'Clean and professional newsletter template',
        thumbnail: 'https://via.placeholder.com/300x400/f0f0f0/666666?text=Newsletter',
        category: 'newsletter',
        isGallery: true,
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      },
      {
        id: 1002,
        name: 'Product Launch',
        description: 'Perfect for announcing new products',
        thumbnail: 'https://via.placeholder.com/300x400/e3f2fd/1976d2?text=Product',
        category: 'promotional',
        isGallery: true,
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      },
      {
        id: 1003,
        name: 'Welcome Series',
        description: 'Warm welcome template for new subscribers',
        thumbnail: 'https://via.placeholder.com/300x400/f3e5f5/7b1fa2?text=Welcome',
        category: 'welcome',
        isGallery: true,
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      }
    ]
    setGalleryTemplates(mockGalleryTemplates)
  }

  const createNewTemplate = (type: string) => {
    // Navigate to template editor with campaign context
    const params = new URLSearchParams({
      type,
      returnTo: 'campaign',
      campaignStep: '3'
    })
    navigate(`/templates/create?${params}`)
  }

  const selectTemplate = (template: Template) => {
    onUpdate({
      template: {
        id: template.id,
        type: template.isGallery ? 'gallery' : 'existing'
      }
    })
  }

  const editTemplate = (templateId: number) => {
    const params = new URLSearchParams({
      returnTo: 'campaign',
      campaignStep: '3'
    })
    navigate(`/templates/edit/${templateId}?${params}`)
  }

  const duplicateTemplate = async (templateId: number) => {
    try {
      const response = await api.post<any>(`/templates/${templateId}/duplicate`)
      loadTemplates()
      selectTemplate({
        id: response.id,
        name: response.name,
        description: response.description,
        thumbnail: response.thumbnail,
        category: 'custom',
        created_at: response.created_at,
        updated_at: response.updated_at
      })
    } catch (error) {
      console.error('Failed to duplicate template:', error)
    }
  }

  const filteredTemplates = (activeTab === 'my-templates' ? templates : galleryTemplates).filter(template => {
    const matchesSearch = searchTerm === '' || 
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (template.description && template.description.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const tabs = [
    { id: 'my-templates', name: 'My Templates', count: templates.length },
    { id: 'gallery', name: 'Template Gallery', count: galleryTemplates.length },
    { id: 'create-new', name: 'Create New', count: null }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Choose Template</h2>
        <p className="text-gray-600">
          Select an existing template, choose from our gallery, or create a new one
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.name}
              {tab.count !== null && (
                <span className="ml-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Search and Filters */}
      {activeTab !== 'create-new' && (
        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
          >
            {categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Content */}
      <div className="min-h-96">
        {activeTab === 'create-new' ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {createOptions.map(option => {
              const Icon = option.icon
              return (
                <button
                  key={option.id}
                  onClick={option.action}
                  className="p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-all group text-left"
                >
                  <div className="text-center">
                    <Icon className="w-12 h-12 text-gray-400 group-hover:text-blue-500 mx-auto mb-4" />
                    <h3 className="font-medium text-gray-900 mb-2">{option.name}</h3>
                    <p className="text-sm text-gray-600">{option.description}</p>
                  </div>
                </button>
              )
            })}
          </div>
        ) : loadingTemplates ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 aspect-[3/4] rounded-lg mb-3"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        ) : filteredTemplates.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm 
                ? 'Try adjusting your search or filters' 
                : activeTab === 'my-templates' 
                  ? 'Create your first template to get started'
                  : 'No templates available in this category'
              }
            </p>
            {activeTab === 'my-templates' && (
              <button
                onClick={() => setActiveTab('create-new')}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2 inline" />
                Create Template
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map(template => (
              <div
                key={template.id}
                className={`group border rounded-lg overflow-hidden transition-all cursor-pointer ${
                  campaignData.template.id === template.id
                    ? 'border-blue-500 ring-2 ring-blue-200'
                    : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                }`}
                onClick={() => selectTemplate(template)}
              >
                {/* Template Preview */}
                <div className="relative aspect-[3/4] bg-gray-100">
                  {template.thumbnail ? (
                    <img
                      src={template.thumbnail}
                      alt={template.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <FileText className="w-12 h-12 text-gray-300" />
                    </div>
                  )}
                  
                  {/* Overlay Actions */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          // Preview template
                        }}
                        className="bg-white text-gray-900 px-3 py-2 rounded-lg text-sm hover:bg-gray-100 transition-colors"
                      >
                        <Eye className="w-4 h-4 inline mr-1" />
                        Preview
                      </button>
                      
                      {!template.isGallery && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            editTemplate(template.id)
                          }}
                          className="bg-white text-gray-900 px-3 py-2 rounded-lg text-sm hover:bg-gray-100 transition-colors"
                        >
                          <Edit className="w-4 h-4 inline mr-1" />
                          Edit
                        </button>
                      )}
                      
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          duplicateTemplate(template.id)
                        }}
                        className="bg-white text-gray-900 px-3 py-2 rounded-lg text-sm hover:bg-gray-100 transition-colors"
                      >
                        <Copy className="w-4 h-4 inline mr-1" />
                        Use
                      </button>
                    </div>
                  </div>

                  {/* Selected Indicator */}
                  {campaignData.template.id === template.id && (
                    <div className="absolute top-2 right-2 bg-blue-500 text-white p-1 rounded-full">
                      <Star className="w-4 h-4 fill-current" />
                    </div>
                  )}

                  {/* Gallery Badge */}
                  {template.isGallery && (
                    <div className="absolute top-2 left-2 bg-purple-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                      Gallery
                    </div>
                  )}
                </div>

                {/* Template Info */}
                <div className="p-4">
                  <h3 className="font-medium text-gray-900 truncate mb-1">
                    {template.name}
                  </h3>
                  {template.description && (
                    <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                      {template.description}
                    </p>
                  )}
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span className="capitalize">{template.category}</span>
                    <div className="flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      <span>{new Date(template.updated_at).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Selected Template Info */}
      {campaignData.template.id && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-full">
              <FileText className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <h3 className="font-medium text-green-900">Template Selected</h3>
              <p className="text-sm text-green-700">
                {filteredTemplates.find(t => t.id === campaignData.template.id)?.name || 'Selected template'} is ready to use
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default MailchimpTemplateSelector
