import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios'

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffFactor: number
}

interface ApiConfig {
  baseURL: string
  timeout: number
  cache: {
    enabled: boolean
    defaultTTL: number
    maxSize: number
  }
  retry: RetryConfig
}

class EnhancedApiService {
  private api: AxiosInstance
  private cache = new Map<string, CacheEntry<any>>()
  private config: ApiConfig

  constructor(config: Partial<ApiConfig> = {}) {
    this.config = {
      baseURL: 'http://localhost:8000/api',
      timeout: 10000,
      cache: {
        enabled: true,
        defaultTTL: 5 * 60 * 1000, // 5 minutes
        maxSize: 100
      },
      retry: {
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 10000,
        backoffFactor: 2
      },
      ...config
    }

    this.api = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
    this.startCacheCleanup()
  }

  private setupInterceptors() {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Response interceptor with retry logic
    this.api.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        const config = error.config as any

        // Don't retry if we've exceeded max retries
        if (!config || config.__retryCount >= this.config.retry.maxRetries) {
          return this.handleError(error)
        }

        // Initialize retry count
        config.__retryCount = config.__retryCount || 0
        config.__retryCount++

        // Calculate delay with exponential backoff
        const delay = Math.min(
          this.config.retry.baseDelay * Math.pow(this.config.retry.backoffFactor, config.__retryCount - 1),
          this.config.retry.maxDelay
        )

        // Only retry on network errors or 5xx status codes
        if (this.shouldRetry(error)) {
          console.log(`Retrying request (${config.__retryCount}/${this.config.retry.maxRetries}) after ${delay}ms`)
          await this.delay(delay)
          return this.api.request(config)
        }

        return this.handleError(error)
      }
    )
  }

  private shouldRetry(error: AxiosError): boolean {
    // Retry on network errors
    if (!error.response) return true
    
    // Retry on 5xx server errors
    if (error.response.status >= 500) return true
    
    // Retry on specific 4xx errors
    if (error.response.status === 408 || error.response.status === 429) return true
    
    return false
  }

  private handleError(error: AxiosError) {
    if (!error.response) {
      throw new Error('Network error. Please check your connection.')
    }

    if (error.response.status === 401) {
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
      throw new Error('Authentication required')
    }

    if (error.response.status >= 500) {
      throw new Error('Server error. Please try again later.')
    }

    const message = (error.response.data as any)?.message || 'Request failed'
    throw new Error(message)
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  private getCacheKey(method: string, url: string, params?: any): string {
    return `${method}:${url}:${JSON.stringify(params || {})}`
  }

  private getFromCache<T>(key: string): T | null {
    if (!this.config.cache.enabled) return null

    const entry = this.cache.get(key)
    if (!entry) return null

    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key)
      return null
    }

    return entry.data
  }

  private setCache<T>(key: string, data: T, ttl?: number): void {
    if (!this.config.cache.enabled) return

    // Remove oldest entries if cache is full
    if (this.cache.size >= this.config.cache.maxSize) {
      const oldestKey = this.cache.keys().next().value
      this.cache.delete(oldestKey)
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.config.cache.defaultTTL
    })
  }

  private startCacheCleanup(): void {
    setInterval(() => {
      const now = Date.now()
      for (const [key, entry] of this.cache.entries()) {
        if (now > entry.timestamp + entry.ttl) {
          this.cache.delete(key)
        }
      }
    }, 60000) // Clean up every minute
  }

  // Public API methods
  async get<T>(url: string, params?: any, options?: { cache?: boolean; ttl?: number }): Promise<T> {
    const cacheKey = this.getCacheKey('GET', url, params)
    
    if (options?.cache !== false) {
      const cached = this.getFromCache<T>(cacheKey)
      if (cached) return cached
    }

    const response: AxiosResponse<T> = await this.api.get(url, { params })
    
    if (options?.cache !== false) {
      this.setCache(cacheKey, response.data, options?.ttl)
    }
    
    return response.data
  }

  async post<T>(url: string, data?: any): Promise<T> {
    const response: AxiosResponse<T> = await this.api.post(url, data)
    
    // Invalidate related cache entries
    this.invalidateCache(url)
    
    return response.data
  }

  async put<T>(url: string, data?: any): Promise<T> {
    const response: AxiosResponse<T> = await this.api.put(url, data)
    
    // Invalidate related cache entries
    this.invalidateCache(url)
    
    return response.data
  }

  async delete<T>(url: string): Promise<T> {
    const response: AxiosResponse<T> = await this.api.delete(url)
    
    // Invalidate related cache entries
    this.invalidateCache(url)
    
    return response.data
  }

  private invalidateCache(url: string): void {
    const urlPattern = url.split('/')[1] // Get the resource type
    for (const key of this.cache.keys()) {
      if (key.includes(urlPattern)) {
        this.cache.delete(key)
      }
    }
  }

  // Cache management
  clearCache(): void {
    this.cache.clear()
  }

  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: this.config.cache.maxSize,
      entries: Array.from(this.cache.entries()).map(([key, entry]) => ({
        key,
        age: Date.now() - entry.timestamp,
        ttl: entry.ttl
      }))
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await this.api.get('/health')
      return true
    } catch {
      return false
    }
  }

  // Batch requests
  async batch<T>(requests: Array<() => Promise<T>>): Promise<T[]> {
    const results = await Promise.allSettled(requests.map(req => req()))
    
    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value
      } else {
        console.error(`Batch request ${index} failed:`, result.reason)
        throw result.reason
      }
    })
  }

  // Upload with progress
  async upload(url: string, file: File, onProgress?: (progress: number) => void): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)

    return this.api.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })
  }
}

// Create singleton instance
export const enhancedApi = new EnhancedApiService()

// Export for custom configurations
export { EnhancedApiService }
export type { ApiConfig, RetryConfig }
