import React, { useState, useMemo } from 'react'
import { useDrag } from 'react-dnd'
import { 
  Type, Image, MousePointer, Minus, Layout, Grid, 
  ShoppingBag, Share2, Calendar, Star, MapPin, 
  Phone, Mail, Globe, Video, FileText, BarChart3,
  Search, ChevronDown, ChevronRight
} from 'lucide-react'
import { TemplateComponent } from '../../../types/templateEditor'

interface ComponentDefinition {
  id: string
  name: string
  icon: React.ReactNode
  category: 'basic' | 'content' | 'layout' | 'advanced'
  description: string
  defaultContent: Record<string, any>
  defaultStyles: Record<string, any>
  componentType: TemplateComponent['type']
  preview: string
}

const MAILCHIMP_COMPONENTS: ComponentDefinition[] = [
  // Basic Blocks
  {
    id: 'text',
    name: 'Text',
    icon: <Type className="w-5 h-5" />,
    category: 'basic',
    description: 'Add a text block',
    defaultContent: { text: 'Your text here...' },
    defaultStyles: { 
      fontSize: '16px', 
      color: '#333333', 
      lineHeight: '1.5',
      fontFamily: 'Arial, sans-serif',
      padding: '16px'
    },
    componentType: 'text',
    preview: 'Aa'
  },
  {
    id: 'image',
    name: 'Image',
    icon: <Image className="w-5 h-5" />,
    category: 'basic',
    description: 'Add an image',
    defaultContent: { 
      src: 'https://via.placeholder.com/600x300/f0f0f0/666666?text=Image', 
      alt: 'Image',
      link: ''
    },
    defaultStyles: { 
      width: '100%', 
      height: 'auto',
      borderRadius: '4px'
    },
    componentType: 'image',
    preview: '🖼️'
  },
  {
    id: 'button',
    name: 'Button',
    icon: <MousePointer className="w-5 h-5" />,
    category: 'basic',
    description: 'Add a call-to-action button',
    defaultContent: { 
      text: 'Click Here', 
      href: '#',
      target: '_blank'
    },
    defaultStyles: { 
      backgroundColor: '#007C89', 
      color: '#ffffff', 
      padding: '12px 24px', 
      borderRadius: '4px',
      textDecoration: 'none',
      display: 'inline-block',
      fontSize: '16px',
      fontWeight: 'bold',
      border: 'none',
      cursor: 'pointer'
    },
    componentType: 'button',
    preview: '🔘'
  },
  {
    id: 'divider',
    name: 'Divider',
    icon: <Minus className="w-5 h-5" />,
    category: 'basic',
    description: 'Add a horizontal divider',
    defaultContent: {},
    defaultStyles: { 
      height: '1px', 
      backgroundColor: '#e1e5e9', 
      border: 'none', 
      margin: '20px 0',
      width: '100%'
    },
    componentType: 'divider',
    preview: '—'
  },

  // Content Blocks
  {
    id: 'heading',
    name: 'Heading',
    icon: <Type className="w-5 h-5" />,
    category: 'content',
    description: 'Add a heading',
    defaultContent: { 
      text: 'Your Heading', 
      level: 'h2' 
    },
    defaultStyles: { 
      fontSize: '28px', 
      fontWeight: 'bold', 
      color: '#333333',
      lineHeight: '1.2',
      margin: '0 0 16px 0',
      fontFamily: 'Arial, sans-serif'
    },
    componentType: 'heading',
    preview: 'H1'
  },
  {
    id: 'social',
    name: 'Social Media',
    icon: <Share2 className="w-5 h-5" />,
    category: 'content',
    description: 'Add social media links',
    defaultContent: {
      platforms: [
        { name: 'facebook', url: '#', icon: 'facebook' },
        { name: 'twitter', url: '#', icon: 'twitter' },
        { name: 'instagram', url: '#', icon: 'instagram' }
      ]
    },
    defaultStyles: {
      display: 'flex',
      gap: '12px',
      justifyContent: 'center',
      padding: '16px'
    },
    componentType: 'social',
    preview: '📱'
  },
  {
    id: 'video',
    name: 'Video',
    icon: <Video className="w-5 h-5" />,
    category: 'content',
    description: 'Embed a video',
    defaultContent: {
      src: '',
      thumbnail: 'https://via.placeholder.com/600x300/f0f0f0/666666?text=Video',
      title: 'Video Title'
    },
    defaultStyles: {
      width: '100%',
      borderRadius: '4px'
    },
    componentType: 'video',
    preview: '▶️'
  },

  // Layout Blocks
  {
    id: 'container',
    name: 'Container',
    icon: <Layout className="w-5 h-5" />,
    category: 'layout',
    description: 'Group other components',
    defaultContent: {},
    defaultStyles: { 
      padding: '20px', 
      backgroundColor: '#ffffff',
      borderRadius: '4px',
      border: '1px solid #e1e5e9'
    },
    componentType: 'container',
    preview: '📦'
  },
  {
    id: 'columns',
    name: 'Columns',
    icon: <Grid className="w-5 h-5" />,
    category: 'layout',
    description: 'Create column layout',
    defaultContent: {
      columns: 2,
      columnContent: ['Column 1 content', 'Column 2 content']
    },
    defaultStyles: {
      display: 'flex',
      gap: '20px',
      padding: '16px'
    },
    componentType: 'columns',
    preview: '⚏'
  },
  {
    id: 'spacer',
    name: 'Spacer',
    icon: <Layout className="w-5 h-5" />,
    category: 'layout',
    description: 'Add vertical spacing',
    defaultContent: {},
    defaultStyles: { 
      height: '40px', 
      width: '100%',
      backgroundColor: 'transparent'
    },
    componentType: 'spacer',
    preview: '⬜'
  },

  // Advanced Blocks
  {
    id: 'product',
    name: 'Product',
    icon: <ShoppingBag className="w-5 h-5" />,
    category: 'advanced',
    description: 'Showcase a product',
    defaultContent: {
      name: 'Product Name',
      price: '$99.99',
      image: 'https://via.placeholder.com/200x200/f0f0f0/666666?text=Product',
      description: 'Product description goes here...',
      buttonText: 'Shop Now',
      buttonLink: '#'
    },
    defaultStyles: {
      border: '1px solid #e1e5e9',
      borderRadius: '8px',
      padding: '20px',
      textAlign: 'center'
    },
    componentType: 'product',
    preview: '🛍️'
  },
  {
    id: 'testimonial',
    name: 'Testimonial',
    icon: <Star className="w-5 h-5" />,
    category: 'advanced',
    description: 'Add customer testimonial',
    defaultContent: {
      quote: 'This is an amazing product! I highly recommend it.',
      author: 'John Doe',
      title: 'CEO, Company',
      avatar: 'https://via.placeholder.com/60x60/f0f0f0/666666?text=JD'
    },
    defaultStyles: {
      padding: '24px',
      backgroundColor: '#f8f9fa',
      borderRadius: '8px',
      fontStyle: 'italic'
    },
    componentType: 'testimonial',
    preview: '💬'
  }
]

const CATEGORIES = [
  { id: 'basic', name: 'Basic Blocks', icon: <Type className="w-4 h-4" /> },
  { id: 'content', name: 'Content Blocks', icon: <FileText className="w-4 h-4" /> },
  { id: 'layout', name: 'Layout Blocks', icon: <Layout className="w-4 h-4" /> },
  { id: 'advanced', name: 'Advanced Blocks', icon: <BarChart3 className="w-4 h-4" /> }
]

interface DraggableComponentProps {
  component: ComponentDefinition
  onAdd: (component: ComponentDefinition) => void
}

const DraggableComponent: React.FC<DraggableComponentProps> = ({ component, onAdd }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'component',
    item: { component },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }))

  return (
    <div
      ref={drag}
      className={`group relative p-3 border border-gray-200 rounded-lg cursor-move hover:border-blue-300 hover:shadow-sm transition-all duration-200 bg-white ${
        isDragging ? 'opacity-50 scale-95' : ''
      }`}
      onClick={() => onAdd(component)}
    >
      <div className="flex flex-col items-center space-y-2">
        <div className="flex items-center justify-center w-12 h-12 bg-gray-50 rounded-lg group-hover:bg-blue-50 transition-colors">
          <div className="text-gray-600 group-hover:text-blue-600">
            {component.icon}
          </div>
        </div>
        <div className="text-center">
          <h4 className="text-sm font-medium text-gray-900 truncate">
            {component.name}
          </h4>
          <p className="text-xs text-gray-500 mt-1 line-clamp-2">
            {component.description}
          </p>
        </div>
      </div>
      
      {/* Preview indicator */}
      <div className="absolute top-2 right-2 text-lg opacity-60">
        {component.preview}
      </div>
    </div>
  )
}

interface MailchimpComponentLibraryProps {
  onComponentAdd: (component: TemplateComponent) => void
}

export const MailchimpComponentLibrary: React.FC<MailchimpComponentLibraryProps> = ({
  onComponentAdd
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('basic')
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['basic']))

  const filteredComponents = useMemo(() => {
    return MAILCHIMP_COMPONENTS.filter(component => {
      const matchesSearch = searchTerm === '' || 
        component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        component.description.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesCategory = selectedCategory === 'all' || component.category === selectedCategory
      
      return matchesSearch && matchesCategory
    })
  }, [searchTerm, selectedCategory])

  const componentsByCategory = useMemo(() => {
    const grouped = MAILCHIMP_COMPONENTS.reduce((acc, component) => {
      if (!acc[component.category]) {
        acc[component.category] = []
      }
      acc[component.category].push(component)
      return acc
    }, {} as Record<string, ComponentDefinition[]>)
    
    return grouped
  }, [])

  const handleComponentAdd = (componentDef: ComponentDefinition) => {
    const newComponent: TemplateComponent = {
      id: `component-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: componentDef.componentType,
      content: { ...componentDef.defaultContent },
      styles: { ...componentDef.defaultStyles },
      position: { x: 0, y: 0 }
    }
    onComponentAdd(newComponent)
  }

  const toggleCategory = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-3">Content</h2>
        
        {/* Search */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search blocks..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
          />
        </div>

        {/* Category Tabs */}
        <div className="flex flex-wrap gap-1">
          <button
            onClick={() => setSelectedCategory('all')}
            className={`px-3 py-1 text-xs rounded-full transition-colors ${
              selectedCategory === 'all'
                ? 'bg-blue-100 text-blue-700 border border-blue-200'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            All
          </button>
          {CATEGORIES.map(category => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-3 py-1 text-xs rounded-full transition-colors ${
                selectedCategory === category.id
                  ? 'bg-blue-100 text-blue-700 border border-blue-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Component List */}
      <div className="flex-1 overflow-y-auto">
        {searchTerm ? (
          // Search results
          <div className="p-4">
            <div className="grid grid-cols-2 gap-3">
              {filteredComponents.map(component => (
                <DraggableComponent
                  key={component.id}
                  component={component}
                  onAdd={handleComponentAdd}
                />
              ))}
            </div>
            {filteredComponents.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <p>No components found</p>
                <p className="text-sm">Try adjusting your search</p>
              </div>
            )}
          </div>
        ) : (
          // Categorized view
          <div>
            {CATEGORIES.map(category => {
              const categoryComponents = componentsByCategory[category.id] || []
              const isExpanded = expandedCategories.has(category.id)
              
              return (
                <div key={category.id} className="border-b border-gray-100">
                  <button
                    onClick={() => toggleCategory(category.id)}
                    className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-2">
                      {category.icon}
                      <span className="font-medium text-gray-900">{category.name}</span>
                      <span className="text-xs text-gray-500">({categoryComponents.length})</span>
                    </div>
                    {isExpanded ? (
                      <ChevronDown className="w-4 h-4 text-gray-400" />
                    ) : (
                      <ChevronRight className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                  
                  {isExpanded && (
                    <div className="px-4 pb-4">
                      <div className="grid grid-cols-2 gap-3">
                        {categoryComponents.map(component => (
                          <DraggableComponent
                            key={component.id}
                            component={component}
                            onAdd={handleComponentAdd}
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}

export default MailchimpComponentLibrary
