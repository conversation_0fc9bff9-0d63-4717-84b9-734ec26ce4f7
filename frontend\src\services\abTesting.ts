interface ABTest {
  id: string
  name: string
  description: string
  status: 'draft' | 'running' | 'completed' | 'paused'
  type: 'subject_line' | 'template' | 'send_time' | 'from_name' | 'content'
  variants: ABVariant[]
  trafficSplit: number[] // Percentage for each variant
  targetAudience: {
    segmentId?: string
    criteria: AudienceCriteria
    size: number
  }
  metrics: {
    primary: 'open_rate' | 'click_rate' | 'conversion_rate' | 'revenue'
    secondary: string[]
  }
  schedule: {
    startDate: string
    endDate?: string
    duration?: number // in hours
    timezone: string
  }
  results?: ABTestResults
  createdBy: string
  createdAt: string
  updatedAt: string
}

interface ABVariant {
  id: string
  name: string
  description?: string
  isControl: boolean
  config: {
    subjectLine?: string
    templateId?: string
    fromName?: string
    fromEmail?: string
    sendTime?: string
    content?: any
  }
  metrics: {
    sent: number
    delivered: number
    opened: number
    clicked: number
    converted: number
    revenue: number
    bounced: number
    unsubscribed: number
  }
}

interface AudienceCriteria {
  includeSegments?: string[]
  excludeSegments?: string[]
  customFilters?: {
    field: string
    operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than'
    value: any
  }[]
  sampleSize?: number
  sampleType?: 'percentage' | 'fixed'
}

interface ABTestResults {
  winner?: string
  confidence: number
  significance: number
  summary: {
    totalSent: number
    testDuration: number
    completedAt: string
  }
  variantResults: {
    [variantId: string]: {
      openRate: number
      clickRate: number
      conversionRate: number
      revenue: number
      confidence: number
      improvement: number
    }
  }
  recommendations: string[]
}

interface TestConfiguration {
  name: string
  description: string
  type: ABTest['type']
  variants: Omit<ABVariant, 'id' | 'metrics'>[]
  trafficSplit: number[]
  targetAudience: ABTest['targetAudience']
  metrics: ABTest['metrics']
  schedule: ABTest['schedule']
  settings: {
    minSampleSize: number
    confidenceLevel: number
    maxDuration: number
    autoWinner: boolean
  }
}

class ABTestingService {
  private baseUrl = 'http://localhost:8000/api'

  // Test management
  async createTest(config: TestConfiguration): Promise<ABTest> {
    const response = await fetch(`${this.baseUrl}/ab-tests`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      },
      body: JSON.stringify(config)
    })

    if (!response.ok) {
      throw new Error('Failed to create A/B test')
    }

    return response.json()
  }

  async getTests(filters?: {
    status?: ABTest['status']
    type?: ABTest['type']
    createdBy?: string
  }): Promise<ABTest[]> {
    const params = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value)
      })
    }

    const response = await fetch(`${this.baseUrl}/ab-tests?${params}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to fetch A/B tests')
    }

    return response.json()
  }

  async getTest(testId: string): Promise<ABTest> {
    const response = await fetch(`${this.baseUrl}/ab-tests/${testId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to fetch A/B test')
    }

    return response.json()
  }

  async updateTest(testId: string, updates: Partial<ABTest>): Promise<ABTest> {
    const response = await fetch(`${this.baseUrl}/ab-tests/${testId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      },
      body: JSON.stringify(updates)
    })

    if (!response.ok) {
      throw new Error('Failed to update A/B test')
    }

    return response.json()
  }

  async deleteTest(testId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/ab-tests/${testId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to delete A/B test')
    }
  }

  // Test execution
  async startTest(testId: string): Promise<ABTest> {
    const response = await fetch(`${this.baseUrl}/ab-tests/${testId}/start`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to start A/B test')
    }

    return response.json()
  }

  async pauseTest(testId: string): Promise<ABTest> {
    const response = await fetch(`${this.baseUrl}/ab-tests/${testId}/pause`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to pause A/B test')
    }

    return response.json()
  }

  async stopTest(testId: string): Promise<ABTest> {
    const response = await fetch(`${this.baseUrl}/ab-tests/${testId}/stop`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to stop A/B test')
    }

    return response.json()
  }

  // Results and analysis
  async getResults(testId: string): Promise<ABTestResults> {
    const response = await fetch(`${this.baseUrl}/ab-tests/${testId}/results`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to fetch A/B test results')
    }

    return response.json()
  }

  async declareWinner(testId: string, variantId: string): Promise<ABTest> {
    const response = await fetch(`${this.baseUrl}/ab-tests/${testId}/winner`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      },
      body: JSON.stringify({ variantId })
    })

    if (!response.ok) {
      throw new Error('Failed to declare winner')
    }

    return response.json()
  }

  // Statistical analysis
  calculateSignificance(
    controlMetrics: { conversions: number; visitors: number },
    variantMetrics: { conversions: number; visitors: number }
  ): { significance: number; confidence: number; improvement: number } {
    const controlRate = controlMetrics.conversions / controlMetrics.visitors
    const variantRate = variantMetrics.conversions / variantMetrics.visitors
    
    const improvement = ((variantRate - controlRate) / controlRate) * 100
    
    // Simplified z-test calculation
    const pooledRate = (controlMetrics.conversions + variantMetrics.conversions) / 
                      (controlMetrics.visitors + variantMetrics.visitors)
    
    const standardError = Math.sqrt(
      pooledRate * (1 - pooledRate) * 
      (1 / controlMetrics.visitors + 1 / variantMetrics.visitors)
    )
    
    const zScore = Math.abs(variantRate - controlRate) / standardError
    
    // Convert z-score to confidence level (simplified)
    const confidence = Math.min(99.9, Math.max(0, (1 - 2 * (1 - this.normalCDF(Math.abs(zScore)))) * 100))
    const significance = zScore
    
    return { significance, confidence, improvement }
  }

  private normalCDF(x: number): number {
    // Approximation of the cumulative distribution function for standard normal distribution
    const t = 1 / (1 + 0.2316419 * Math.abs(x))
    const d = 0.3989423 * Math.exp(-x * x / 2)
    const prob = d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))))
    
    return x > 0 ? 1 - prob : prob
  }

  // Audience estimation
  async estimateAudience(criteria: AudienceCriteria): Promise<number> {
    const response = await fetch(`${this.baseUrl}/audience/estimate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      },
      body: JSON.stringify(criteria)
    })

    if (!response.ok) {
      throw new Error('Failed to estimate audience size')
    }

    const result = await response.json()
    return result.size
  }

  // Test templates and presets
  getTestTemplates(): Array<{
    name: string
    description: string
    type: ABTest['type']
    variants: string[]
    recommendedMetrics: string[]
  }> {
    return [
      {
        name: 'Subject Line Test',
        description: 'Test different subject lines to improve open rates',
        type: 'subject_line',
        variants: ['Control Subject', 'Variant A', 'Variant B'],
        recommendedMetrics: ['open_rate', 'click_rate']
      },
      {
        name: 'Template Design Test',
        description: 'Compare different email templates',
        type: 'template',
        variants: ['Current Template', 'New Design'],
        recommendedMetrics: ['click_rate', 'conversion_rate']
      },
      {
        name: 'Send Time Optimization',
        description: 'Find the best time to send emails',
        type: 'send_time',
        variants: ['Morning', 'Afternoon', 'Evening'],
        recommendedMetrics: ['open_rate', 'click_rate']
      },
      {
        name: 'From Name Test',
        description: 'Test different sender names',
        type: 'from_name',
        variants: ['Company Name', 'Personal Name', 'Department'],
        recommendedMetrics: ['open_rate', 'click_rate']
      }
    ]
  }

  // Validation
  validateTestConfiguration(config: TestConfiguration): string[] {
    const errors: string[] = []

    if (!config.name.trim()) {
      errors.push('Test name is required')
    }

    if (config.variants.length < 2) {
      errors.push('At least 2 variants are required')
    }

    if (config.trafficSplit.length !== config.variants.length) {
      errors.push('Traffic split must match number of variants')
    }

    const totalSplit = config.trafficSplit.reduce((sum, split) => sum + split, 0)
    if (Math.abs(totalSplit - 100) > 0.01) {
      errors.push('Traffic split must total 100%')
    }

    if (!config.variants.some(v => v.isControl)) {
      errors.push('One variant must be marked as control')
    }

    if (config.schedule.startDate && new Date(config.schedule.startDate) < new Date()) {
      errors.push('Start date cannot be in the past')
    }

    return errors
  }

  // Recommendations
  generateRecommendations(test: ABTest): string[] {
    const recommendations: string[] = []

    if (!test.results) {
      return ['Test is still running. Check back later for results.']
    }

    const { variantResults } = test.results

    // Find best performing variant
    const variants = Object.entries(variantResults)
    const bestVariant = variants.reduce((best, current) => {
      const metric = test.metrics.primary
      const bestValue = best[1][metric] || 0
      const currentValue = current[1][metric] || 0
      return currentValue > bestValue ? current : best
    })

    if (bestVariant[1].confidence > 95) {
      recommendations.push(`Variant "${bestVariant[0]}" is the clear winner with ${bestVariant[1].confidence.toFixed(1)}% confidence`)
    } else if (bestVariant[1].confidence > 80) {
      recommendations.push(`Variant "${bestVariant[0]}" shows promise but needs more data for statistical significance`)
    } else {
      recommendations.push('No clear winner detected. Consider running the test longer or increasing sample size')
    }

    // Additional insights
    if (test.type === 'subject_line' && bestVariant[1].improvement > 10) {
      recommendations.push('Consider applying the winning subject line style to future campaigns')
    }

    if (test.type === 'template' && bestVariant[1].improvement > 5) {
      recommendations.push('Update your default template with the winning design elements')
    }

    return recommendations
  }
}

// Create singleton instance
export const abTestingService = new ABTestingService()

export { ABTestingService }
export type { ABTest, ABVariant, ABTestResults, TestConfiguration, AudienceCriteria }
