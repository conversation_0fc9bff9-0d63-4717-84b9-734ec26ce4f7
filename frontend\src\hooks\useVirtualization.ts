import { useState, useEffect, useMemo, useCallback } from 'react'

interface VirtualizationOptions {
  itemHeight: number
  containerHeight: number
  overscan?: number
  items: any[]
}

interface VirtualizedItem {
  index: number
  item: any
  style: React.CSSProperties
}

export const useVirtualization = ({
  itemHeight,
  containerHeight,
  overscan = 5,
  items
}: VirtualizationOptions) => {
  const [scrollTop, setScrollTop] = useState(0)

  const totalHeight = items.length * itemHeight

  const visibleRange = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    )

    return {
      start: Math.max(0, startIndex - overscan),
      end: Math.min(items.length - 1, endIndex + overscan)
    }
  }, [scrollTop, itemHeight, containerHeight, overscan, items.length])

  const virtualizedItems: VirtualizedItem[] = useMemo(() => {
    const result: VirtualizedItem[] = []
    
    for (let i = visibleRange.start; i <= visibleRange.end; i++) {
      result.push({
        index: i,
        item: items[i],
        style: {
          position: 'absolute',
          top: i * itemHeight,
          left: 0,
          right: 0,
          height: itemHeight
        }
      })
    }
    
    return result
  }, [visibleRange, items, itemHeight])

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop)
  }, [])

  const scrollToIndex = useCallback((index: number) => {
    const targetScrollTop = index * itemHeight
    setScrollTop(targetScrollTop)
  }, [itemHeight])

  return {
    virtualizedItems,
    totalHeight,
    handleScroll,
    scrollToIndex,
    visibleRange
  }
}

// Hook for optimized component rendering
export const useOptimizedRendering = <T>(
  items: T[],
  dependencies: any[] = []
) => {
  const [renderKey, setRenderKey] = useState(0)

  const memoizedItems = useMemo(() => items, dependencies)

  const forceRerender = useCallback(() => {
    setRenderKey(prev => prev + 1)
  }, [])

  const shouldUpdate = useCallback((prevItems: T[], nextItems: T[]) => {
    if (prevItems.length !== nextItems.length) return true
    
    return prevItems.some((item, index) => {
      const nextItem = nextItems[index]
      return JSON.stringify(item) !== JSON.stringify(nextItem)
    })
  }, [])

  return {
    items: memoizedItems,
    renderKey,
    forceRerender,
    shouldUpdate
  }
}

// Hook for debounced updates
export const useDebouncedUpdate = <T>(
  value: T,
  delay: number,
  callback: (value: T) => void
) => {
  const [debouncedValue, setDebouncedValue] = useState(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
      callback(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay, callback])

  return debouncedValue
}

// Hook for throttled operations
export const useThrottledCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const [lastCall, setLastCall] = useState(0)

  return useCallback((...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      setLastCall(now)
      return callback(...args)
    }
  }, [callback, delay, lastCall]) as T
}

// Hook for measuring component performance
export const usePerformanceMonitor = (componentName: string) => {
  const [metrics, setMetrics] = useState({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0
  })

  useEffect(() => {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      setMetrics(prev => {
        const newRenderCount = prev.renderCount + 1
        const newAverageRenderTime = 
          (prev.averageRenderTime * prev.renderCount + renderTime) / newRenderCount
        
        return {
          renderCount: newRenderCount,
          lastRenderTime: renderTime,
          averageRenderTime: newAverageRenderTime
        }
      })

      // Log performance in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName} render time: ${renderTime.toFixed(2)}ms`)
      }
    }
  })

  return metrics
}

// Hook for memory usage monitoring
export const useMemoryMonitor = () => {
  const [memoryUsage, setMemoryUsage] = useState<{
    used: number
    total: number
    limit: number
  } | null>(null)

  useEffect(() => {
    const updateMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        setMemoryUsage({
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        })
      }
    }

    updateMemoryUsage()
    const interval = setInterval(updateMemoryUsage, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [])

  return memoryUsage
}

// Hook for intersection observer (lazy loading)
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [element, setElement] = useState<Element | null>(null)

  useEffect(() => {
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => setIsIntersecting(entry.isIntersecting),
      options
    )

    observer.observe(element)

    return () => observer.disconnect()
  }, [element, options])

  return [setElement, isIntersecting] as const
}
