<?php

namespace App\Controllers;

use App\Database;
use PDO;

abstract class BaseController
{
    protected PDO $db;
    
    public function __construct()
    {
        $this->db = Database::getConnection();
    }
    
    protected function jsonResponse(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        echo json_encode($data);
    }
    
    protected function successResponse($data = null, string $message = 'Success'): void
    {
        $response = [
            'success' => true,
            'message' => $message
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        $this->jsonResponse($response);
    }
    
    protected function errorResponse(string $message, int $statusCode = 400, array $errors = []): void
    {
        $response = [
            'success' => false,
            'message' => $message
        ];
        
        if (!empty($errors)) {
            $response['errors'] = $errors;
        }
        
        $this->jsonResponse($response, $statusCode);
    }
    
    protected function getJsonInput(): array
    {
        $input = file_get_contents('php://input');
        return json_decode($input, true) ?? [];
    }
    
    protected function validateRequired(array $data, array $required): array
    {
        $errors = [];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty(trim($data[$field]))) {
                $errors[$field] = ["The {$field} field is required"];
            }
        }
        
        return $errors;
    }
    
    protected function validateEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    protected function getCurrentUser(): ?array
    {
        // Get authorization header from $_SERVER (more reliable with built-in server)
        $authHeader = '';

        // Try different ways to get the Authorization header
        if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $authHeader = $_SERVER['HTTP_AUTHORIZATION'];
        } elseif (isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION'])) {
            $authHeader = $_SERVER['REDIRECT_HTTP_AUTHORIZATION'];
        } elseif (function_exists('apache_request_headers')) {
            $headers = apache_request_headers();
            $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        } elseif (function_exists('getallheaders')) {
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
        }

        if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
            return null;
        }

        $token = substr($authHeader, 7);

        try {
            // Improved token validation with caching
            $decoded = $this->validateJWT($token);
            if (!$decoded) {
                return null;
            }

            // Get user from database
            $stmt = $this->db->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$decoded['user_id']]);
            $user = $stmt->fetch();

            return $user ?: null;
        } catch (\Exception $e) {
            error_log("Token validation error: " . $e->getMessage());
            return null;
        }
    }

    protected function validateJWT(string $token): ?array
    {
        try {
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return null;
            }

            [$header, $payload, $signature] = $parts;

            // Decode header and payload
            $headerData = json_decode(base64_decode($header), true);
            $payloadData = json_decode(base64_decode($payload), true);

            if (!$headerData || !$payloadData) {
                return null;
            }

            // Check expiration
            if (isset($payloadData['exp']) && $payloadData['exp'] < time()) {
                return null;
            }

            // Verify signature
            $expectedSignature = base64_encode(hash_hmac('sha256', $header . '.' . $payload, $_ENV['JWT_SECRET'] ?? 'default-secret', true));
            if (!hash_equals($expectedSignature, $signature)) {
                return null;
            }

            return $payloadData;
        } catch (\Exception $e) {
            return null;
        }
    }
    
    protected function requireAuth(): array
    {
        $user = $this->getCurrentUser();
        
        if (!$user) {
            $this->errorResponse('Unauthorized', 401);
            exit;
        }
        
        return $user;
    }
    
    protected function generateToken(array $user): string
    {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode([
            'user_id' => $user['id'],
            'email' => $user['email'],
            'iat' => time(),
            'exp' => time() + (24 * 60 * 60) // 24 hours
        ]);

        $headerEncoded = base64_encode($header);
        $payloadEncoded = base64_encode($payload);

        $signature = base64_encode(hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $_ENV['JWT_SECRET'] ?? 'default-secret', true));

        return $headerEncoded . '.' . $payloadEncoded . '.' . $signature;
    }

    protected function sanitizeInput(string $input): string
    {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }

    protected function validateLength(string $value, int $min = 0, int $max = 255): bool
    {
        $length = strlen($value);
        return $length >= $min && $length <= $max;
    }

    protected function rateLimitCheck(string $identifier, int $maxAttempts = 5, int $timeWindow = 300): bool
    {
        // Simple rate limiting implementation
        // In production, use Redis or a proper rate limiting service
        $cacheKey = "rate_limit_{$identifier}";
        $attempts = $_SESSION[$cacheKey] ?? [];

        // Clean old attempts
        $now = time();
        $attempts = array_filter($attempts, function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        });

        if (count($attempts) >= $maxAttempts) {
            return false;
        }

        $attempts[] = $now;
        $_SESSION[$cacheKey] = $attempts;

        return true;
    }

    protected function validatePasswordStrength(string $password): bool
    {
        // Check for at least one uppercase letter, one lowercase letter, and one number
        return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/', $password) === 1;
    }
}
