import { TemplateComponent, TemplateSettings } from '../types/templateEditor'

/**
 * Parse HTML content and extract template components
 * This is a simplified parser that attempts to convert HTML elements to template components
 */
export const parseHtmlToComponents = (htmlContent: string): TemplateComponent[] => {
  const components: TemplateComponent[] = []
  
  try {
    // Create a temporary DOM element to parse the HTML
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = htmlContent
    
    // Counter for generating unique IDs
    let componentCounter = 0
    
    const parseElement = (element: Element, parentPosition = { x: 0, y: 0 }): TemplateComponent | null => {
      const tagName = element.tagName.toLowerCase()
      const id = `parsed-${componentCounter++}`
      
      // Extract styles from the element
      const computedStyles = window.getComputedStyle(element)
      const styles: Record<string, any> = {}
      
      // Common style properties to extract
      const styleProps = [
        'color', 'backgroundColor', 'fontSize', 'fontFamily', 'fontWeight',
        'textAlign', 'padding', 'margin', 'border', 'borderRadius',
        'width', 'height', 'display', 'position', 'top', 'left'
      ]
      
      styleProps.forEach(prop => {
        const value = computedStyles.getPropertyValue(prop.replace(/([A-Z])/g, '-$1').toLowerCase())
        if (value && value !== 'initial' && value !== 'normal') {
          styles[prop] = value
        }
      })
      
      // Extract inline styles
      if (element.getAttribute('style')) {
        const inlineStyles = element.getAttribute('style')!.split(';')
        inlineStyles.forEach(style => {
          const [property, value] = style.split(':').map(s => s.trim())
          if (property && value) {
            const camelCaseProperty = property.replace(/-([a-z])/g, (g) => g[1].toUpperCase())
            styles[camelCaseProperty] = value
          }
        })
      }
      
      // Determine component type and content based on tag
      let componentType: TemplateComponent['type'] = 'text'
      let content: Record<string, any> = {}
      
      switch (tagName) {
        case 'h1':
        case 'h2':
        case 'h3':
        case 'h4':
        case 'h5':
        case 'h6':
          componentType = 'heading'
          content = {
            text: element.textContent || '',
            level: tagName
          }
          break
          
        case 'p':
        case 'span':
        case 'div':
          if (element.textContent?.trim()) {
            componentType = 'text'
            content = {
              text: element.textContent
            }
          } else {
            componentType = 'container'
            content = {}
          }
          break
          
        case 'img':
          componentType = 'image'
          content = {
            src: element.getAttribute('src') || '',
            alt: element.getAttribute('alt') || '',
            width: element.getAttribute('width') || 'auto',
            height: element.getAttribute('height') || 'auto'
          }
          break
          
        case 'a':
          if (element.textContent?.trim()) {
            componentType = 'button'
            content = {
              text: element.textContent,
              href: element.getAttribute('href') || '#',
              target: element.getAttribute('target') || '_self'
            }
          }
          break
          
        case 'button':
          componentType = 'button'
          content = {
            text: element.textContent || 'Button',
            type: element.getAttribute('type') || 'button'
          }
          break
          
        case 'hr':
          componentType = 'divider'
          content = {}
          break
          
        default:
          // For unknown elements, treat as container if it has children, text if it has text content
          if (element.children.length > 0) {
            componentType = 'container'
            content = {}
          } else if (element.textContent?.trim()) {
            componentType = 'text'
            content = {
              text: element.textContent
            }
          } else {
            return null // Skip empty unknown elements
          }
      }
      
      // Calculate position (simplified - just use incremental positioning)
      const position = {
        x: parentPosition.x + (componentCounter * 10),
        y: parentPosition.y + (componentCounter * 50)
      }
      
      const component: TemplateComponent = {
        id,
        type: componentType,
        content,
        styles,
        position,
        children: []
      }
      
      // Parse child elements for container components
      if (componentType === 'container' && element.children.length > 0) {
        const children: TemplateComponent[] = []
        Array.from(element.children).forEach(child => {
          const childComponent = parseElement(child, position)
          if (childComponent) {
            children.push(childComponent)
          }
        })
        component.children = children
      }
      
      return component
    }
    
    // Parse all top-level elements
    Array.from(tempDiv.children).forEach(element => {
      const component = parseElement(element)
      if (component) {
        components.push(component)
      }
    })
    
    // If no components were parsed but there's text content, create a text component
    if (components.length === 0 && tempDiv.textContent?.trim()) {
      components.push({
        id: 'parsed-text-0',
        type: 'text',
        content: { text: tempDiv.textContent.trim() },
        styles: {},
        position: { x: 50, y: 50 }
      })
    }
    
  } catch (error) {
    console.error('Error parsing HTML to components:', error)
    // Return a fallback text component with the raw HTML
    return [{
      id: 'fallback-html',
      type: 'text',
      content: { text: 'Custom HTML content (could not parse)' },
      styles: {},
      position: { x: 50, y: 50 }
    }]
  }
  
  return components
}

/**
 * Convert template components back to HTML
 * This is used when switching from visual to code mode
 */
export const componentsToHtml = (components: TemplateComponent[], _settings?: TemplateSettings): string => {
  const renderComponent = (component: TemplateComponent): string => {
    const styles = Object.entries(component.styles || {})
      .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
      .join('; ')
    
    const styleAttr = styles ? ` style="${styles}"` : ''
    
    switch (component.type) {
      case 'text':
        return `<p${styleAttr}>${component.content?.text || ''}</p>`
        
      case 'heading':
        const level = component.content?.level || 'h2'
        return `<${level}${styleAttr}>${component.content?.text || ''}</${level}>`
        
      case 'image':
        return `<img src="${component.content?.src || ''}" alt="${component.content?.alt || ''}"${styleAttr} />`
        
      case 'button':
        if (component.content?.href) {
          return `<a href="${component.content.href}" target="${component.content.target || '_self'}"${styleAttr}>${component.content.text || 'Button'}</a>`
        } else {
          return `<button type="${component.content?.type || 'button'}"${styleAttr}>${component.content?.text || 'Button'}</button>`
        }
        
      case 'divider':
        return `<hr${styleAttr} />`
        
      case 'spacer':
        return `<div${styleAttr}>&nbsp;</div>`
        
      case 'container':
        const childrenHtml = component.children?.map(renderComponent).join('\n') || ''
        return `<div${styleAttr}>\n${childrenHtml}\n</div>`
        
      default:
        return `<div${styleAttr}>${component.content?.text || ''}</div>`
    }
  }
  
  return components.map(renderComponent).join('\n\n')
}
