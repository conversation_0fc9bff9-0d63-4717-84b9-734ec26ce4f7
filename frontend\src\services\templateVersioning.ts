interface TemplateVersion {
  id: string
  templateId: string
  version: string
  name: string
  description?: string
  content: any
  components: any[]
  settings: any
  createdBy: string
  createdAt: string
  tags: string[]
  isPublished: boolean
  parentVersion?: string
  changeLog: VersionChange[]
  metadata: {
    componentCount: number
    size: number
    complexity: number
  }
}

interface VersionChange {
  type: 'component_added' | 'component_removed' | 'component_modified' | 'settings_changed' | 'content_changed'
  componentId?: string
  componentType?: string
  description: string
  timestamp: string
  userId: string
}

interface VersionComparison {
  added: any[]
  removed: any[]
  modified: Array<{
    id: string
    changes: Record<string, { old: any; new: any }>
  }>
  summary: {
    totalChanges: number
    componentsAffected: number
    changeTypes: Record<string, number>
  }
}

interface BranchInfo {
  name: string
  baseVersion: string
  headVersion: string
  description?: string
  createdBy: string
  createdAt: string
  isActive: boolean
}

class TemplateVersioningService {
  private baseUrl = 'http://localhost:8000/api'

  // Version management
  async createVersion(
    templateId: string,
    versionData: {
      name: string
      description?: string
      content: any
      components: any[]
      settings: any
      tags?: string[]
      parentVersion?: string
    }
  ): Promise<TemplateVersion> {
    const changeLog = this.generateChangeLog(versionData.components, versionData.parentVersion)
    const metadata = this.calculateMetadata(versionData.components, versionData.content)

    const version: Omit<TemplateVersion, 'id' | 'createdAt' | 'createdBy'> = {
      templateId,
      version: await this.generateVersionNumber(templateId),
      name: versionData.name,
      description: versionData.description,
      content: versionData.content,
      components: versionData.components,
      settings: versionData.settings,
      tags: versionData.tags || [],
      isPublished: false,
      parentVersion: versionData.parentVersion,
      changeLog,
      metadata
    }

    const response = await fetch(`${this.baseUrl}/templates/${templateId}/versions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      },
      body: JSON.stringify(version)
    })

    if (!response.ok) {
      throw new Error('Failed to create version')
    }

    return response.json()
  }

  async getVersions(templateId: string): Promise<TemplateVersion[]> {
    const response = await fetch(`${this.baseUrl}/templates/${templateId}/versions`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to fetch versions')
    }

    return response.json()
  }

  async getVersion(templateId: string, versionId: string): Promise<TemplateVersion> {
    const response = await fetch(`${this.baseUrl}/templates/${templateId}/versions/${versionId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to fetch version')
    }

    return response.json()
  }

  async publishVersion(templateId: string, versionId: string): Promise<TemplateVersion> {
    const response = await fetch(`${this.baseUrl}/templates/${templateId}/versions/${versionId}/publish`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to publish version')
    }

    return response.json()
  }

  async revertToVersion(templateId: string, versionId: string): Promise<TemplateVersion> {
    const response = await fetch(`${this.baseUrl}/templates/${templateId}/revert/${versionId}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to revert to version')
    }

    return response.json()
  }

  // Version comparison
  async compareVersions(
    templateId: string,
    fromVersionId: string,
    toVersionId: string
  ): Promise<VersionComparison> {
    const [fromVersion, toVersion] = await Promise.all([
      this.getVersion(templateId, fromVersionId),
      this.getVersion(templateId, toVersionId)
    ])

    return this.diffVersions(fromVersion, toVersion)
  }

  private diffVersions(fromVersion: TemplateVersion, toVersion: TemplateVersion): VersionComparison {
    const fromComponents = new Map(fromVersion.components.map(c => [c.id, c]))
    const toComponents = new Map(toVersion.components.map(c => [c.id, c]))

    const added: any[] = []
    const removed: any[] = []
    const modified: Array<{ id: string; changes: Record<string, { old: any; new: any }> }> = []

    // Find added components
    for (const [id, component] of toComponents) {
      if (!fromComponents.has(id)) {
        added.push(component)
      }
    }

    // Find removed and modified components
    for (const [id, component] of fromComponents) {
      if (!toComponents.has(id)) {
        removed.push(component)
      } else {
        const changes = this.diffComponents(component, toComponents.get(id)!)
        if (Object.keys(changes).length > 0) {
          modified.push({ id, changes })
        }
      }
    }

    const totalChanges = added.length + removed.length + modified.length
    const componentsAffected = new Set([
      ...added.map(c => c.id),
      ...removed.map(c => c.id),
      ...modified.map(c => c.id)
    ]).size

    const changeTypes: Record<string, number> = {
      added: added.length,
      removed: removed.length,
      modified: modified.length
    }

    return {
      added,
      removed,
      modified,
      summary: {
        totalChanges,
        componentsAffected,
        changeTypes
      }
    }
  }

  private diffComponents(oldComponent: any, newComponent: any): Record<string, { old: any; new: any }> {
    const changes: Record<string, { old: any; new: any }> = {}

    const compareObjects = (obj1: any, obj2: any, path: string = '') => {
      for (const key in obj2) {
        const currentPath = path ? `${path}.${key}` : key
        
        if (!(key in obj1)) {
          changes[currentPath] = { old: undefined, new: obj2[key] }
        } else if (typeof obj2[key] === 'object' && obj2[key] !== null) {
          if (typeof obj1[key] === 'object' && obj1[key] !== null) {
            compareObjects(obj1[key], obj2[key], currentPath)
          } else {
            changes[currentPath] = { old: obj1[key], new: obj2[key] }
          }
        } else if (obj1[key] !== obj2[key]) {
          changes[currentPath] = { old: obj1[key], new: obj2[key] }
        }
      }

      // Check for removed properties
      for (const key in obj1) {
        if (!(key in obj2)) {
          const currentPath = path ? `${path}.${key}` : key
          changes[currentPath] = { old: obj1[key], new: undefined }
        }
      }
    }

    compareObjects(oldComponent, newComponent)
    return changes
  }

  // Branching
  async createBranch(
    templateId: string,
    branchName: string,
    baseVersionId: string,
    description?: string
  ): Promise<BranchInfo> {
    const response = await fetch(`${this.baseUrl}/templates/${templateId}/branches`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      },
      body: JSON.stringify({
        name: branchName,
        baseVersion: baseVersionId,
        description
      })
    })

    if (!response.ok) {
      throw new Error('Failed to create branch')
    }

    return response.json()
  }

  async getBranches(templateId: string): Promise<BranchInfo[]> {
    const response = await fetch(`${this.baseUrl}/templates/${templateId}/branches`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to fetch branches')
    }

    return response.json()
  }

  async mergeBranch(
    templateId: string,
    sourceBranch: string,
    targetBranch: string,
    strategy: 'auto' | 'manual' = 'auto'
  ): Promise<{ success: boolean; conflicts?: any[] }> {
    const response = await fetch(`${this.baseUrl}/templates/${templateId}/branches/merge`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
      },
      body: JSON.stringify({
        sourceBranch,
        targetBranch,
        strategy
      })
    })

    if (!response.ok) {
      throw new Error('Failed to merge branch')
    }

    return response.json()
  }

  // Utility methods
  private async generateVersionNumber(templateId: string): Promise<string> {
    const versions = await this.getVersions(templateId)
    const versionNumbers = versions.map(v => {
      const parts = v.version.split('.').map(Number)
      return parts.length === 3 ? parts : [1, 0, 0]
    })

    if (versionNumbers.length === 0) {
      return '1.0.0'
    }

    const latest = versionNumbers.reduce((max, current) => {
      if (current[0] > max[0]) return current
      if (current[0] === max[0] && current[1] > max[1]) return current
      if (current[0] === max[0] && current[1] === max[1] && current[2] > max[2]) return current
      return max
    })

    return `${latest[0]}.${latest[1]}.${latest[2] + 1}`
  }

  private generateChangeLog(components: any[], parentVersionId?: string): VersionChange[] {
    // This would typically compare with the parent version
    // For now, return a basic change log
    return [
      {
        type: 'content_changed',
        description: 'Template content updated',
        timestamp: new Date().toISOString(),
        userId: 'current_user' // Would be actual user ID
      }
    ]
  }

  private calculateMetadata(components: any[], content: any): TemplateVersion['metadata'] {
    const componentCount = components.length
    const size = JSON.stringify({ components, content }).length
    
    // Simple complexity calculation based on component types and nesting
    const complexity = components.reduce((acc, component) => {
      let score = 1
      if (component.type === 'container') score += 2
      if (component.children?.length > 0) score += component.children.length
      if (Object.keys(component.styles || {}).length > 5) score += 1
      return acc + score
    }, 0)

    return {
      componentCount,
      size,
      complexity
    }
  }

  // Auto-save and draft management
  async saveDraft(templateId: string, content: any): Promise<void> {
    const draftKey = `template_draft_${templateId}`
    const draft = {
      content,
      timestamp: Date.now(),
      templateId
    }

    localStorage.setItem(draftKey, JSON.stringify(draft))
  }

  getDraft(templateId: string): any | null {
    const draftKey = `template_draft_${templateId}`
    const draftData = localStorage.getItem(draftKey)
    
    if (!draftData) return null

    try {
      const draft = JSON.parse(draftData)
      
      // Check if draft is not too old (24 hours)
      if (Date.now() - draft.timestamp > 24 * 60 * 60 * 1000) {
        localStorage.removeItem(draftKey)
        return null
      }

      return draft.content
    } catch {
      localStorage.removeItem(draftKey)
      return null
    }
  }

  clearDraft(templateId: string): void {
    const draftKey = `template_draft_${templateId}`
    localStorage.removeItem(draftKey)
  }
}

// Create singleton instance
export const templateVersioning = new TemplateVersioningService()

export { TemplateVersioningService }
export type { TemplateVersion, VersionChange, VersionComparison, BranchInfo }
