import React from 'react'
import { TemplateComponent } from '../../types/templateEditor'
import { 
  ArrowUp, 
  ArrowDown, 
  Copy, 
  Trash2, 
  RotateCcw, 
  RotateCw,
  AlignLeft,
  AlignCenter,
  AlignRight
} from 'lucide-react'

interface ComponentToolbarProps {
  component: TemplateComponent
  onZIndexChange: (direction: 'up' | 'down') => void
  onDuplicate: () => void
  onDelete: () => void
  onAlign?: (alignment: 'left' | 'center' | 'right') => void
  onRotate?: (direction: 'left' | 'right') => void
}

export const ComponentToolbar: React.FC<ComponentToolbarProps> = ({
  component,
  onZIndexChange,
  onDuplicate,
  onDelete,
  onAlign,
  onRotate
}) => {
  const handleAlign = (alignment: 'left' | 'center' | 'right') => {
    onAlign?.(alignment)
  }

  const handleRotate = (direction: 'left' | 'right') => {
    onRotate?.(direction)
  }

  return (
    <div 
      className="absolute -top-10 left-0 flex items-center space-x-1 bg-white border border-gray-200 rounded-lg shadow-lg p-1 z-50"
      style={{
        left: component.position?.x || 0,
        top: (component.position?.y || 0) - 40
      }}
    >
      {/* Layer controls */}
      <button
        onClick={() => onZIndexChange('up')}
        className="p-1 hover:bg-gray-100 rounded text-gray-600 hover:text-gray-900"
        title="Bring forward"
      >
        <ArrowUp className="w-4 h-4" />
      </button>
      
      <button
        onClick={() => onZIndexChange('down')}
        className="p-1 hover:bg-gray-100 rounded text-gray-600 hover:text-gray-900"
        title="Send backward"
      >
        <ArrowDown className="w-4 h-4" />
      </button>

      <div className="w-px h-4 bg-gray-300" />

      {/* Alignment controls */}
      <button
        onClick={() => handleAlign('left')}
        className="p-1 hover:bg-gray-100 rounded text-gray-600 hover:text-gray-900"
        title="Align left"
      >
        <AlignLeft className="w-4 h-4" />
      </button>
      
      <button
        onClick={() => handleAlign('center')}
        className="p-1 hover:bg-gray-100 rounded text-gray-600 hover:text-gray-900"
        title="Align center"
      >
        <AlignCenter className="w-4 h-4" />
      </button>
      
      <button
        onClick={() => handleAlign('right')}
        className="p-1 hover:bg-gray-100 rounded text-gray-600 hover:text-gray-900"
        title="Align right"
      >
        <AlignRight className="w-4 h-4" />
      </button>

      <div className="w-px h-4 bg-gray-300" />

      {/* Rotation controls */}
      <button
        onClick={() => handleRotate('left')}
        className="p-1 hover:bg-gray-100 rounded text-gray-600 hover:text-gray-900"
        title="Rotate left"
      >
        <RotateCcw className="w-4 h-4" />
      </button>
      
      <button
        onClick={() => handleRotate('right')}
        className="p-1 hover:bg-gray-100 rounded text-gray-600 hover:text-gray-900"
        title="Rotate right"
      >
        <RotateCw className="w-4 h-4" />
      </button>

      <div className="w-px h-4 bg-gray-300" />

      {/* Action controls */}
      <button
        onClick={onDuplicate}
        className="p-1 hover:bg-gray-100 rounded text-gray-600 hover:text-gray-900"
        title="Duplicate"
      >
        <Copy className="w-4 h-4" />
      </button>
      
      <button
        onClick={onDelete}
        className="p-1 hover:bg-red-100 rounded text-red-600 hover:text-red-700"
        title="Delete"
      >
        <Trash2 className="w-4 h-4" />
      </button>
    </div>
  )
}
