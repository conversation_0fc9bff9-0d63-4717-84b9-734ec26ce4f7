// Test utilities and helpers for the email application

export const mockUser = {
  id: '1',
  name: 'Test User',
  email: '<EMAIL>',
  created_at: '2024-01-01T00:00:00Z'
}

export const mockTemplate = {
  id: '1',
  name: 'Test Template',
  description: 'A test template',
  html_content: '<div>Test content</div>',
  components: [],
  settings: {},
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

export const mockComponent = {
  id: 'test-component-1',
  type: 'text' as const,
  content: { text: 'Test text' },
  styles: { fontSize: '16px', color: '#000000' },
  position: { x: 0, y: 0 }
}

export const mockCampaign = {
  id: '1',
  name: 'Test Campaign',
  subject: 'Test Subject',
  template_id: '1',
  status: 'draft' as const,
  total_recipients: 0,
  sent_count: 0,
  delivered_count: 0,
  opened_count: 0,
  clicked_count: 0,
  bounced_count: 0,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

export const mockContact = {
  id: '1',
  email: '<EMAIL>',
  name: 'Test Contact',
  status: 'active' as const,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

// API response mocks
export const mockApiResponse = {
  success: true,
  message: 'Success',
  data: null
}

export const mockApiError = {
  success: false,
  message: 'Error occurred',
  errors: {}
}

// Local storage mock for testing
export const mockLocalStorage = (() => {
  let store: Record<string, string> = {}

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString()
    },
    removeItem: (key: string) => {
      delete store[key]
    },
    clear: () => {
      store = {}
    }
  }
})()

// Test data generators
export const generateTestTemplate = (overrides: Partial<typeof mockTemplate> = {}) => ({
  ...mockTemplate,
  ...overrides,
  id: Math.random().toString(36).substr(2, 9)
})

export const generateTestComponent = (overrides: Partial<typeof mockComponent> = {}) => ({
  ...mockComponent,
  ...overrides,
  id: Math.random().toString(36).substr(2, 9)
})

export const generateTestCampaign = (overrides: Partial<typeof mockCampaign> = {}) => ({
  ...mockCampaign,
  ...overrides,
  id: Math.random().toString(36).substr(2, 9)
})

export const generateTestContact = (overrides: Partial<typeof mockContact> = {}) => ({
  ...mockContact,
  ...overrides,
  id: Math.random().toString(36).substr(2, 9),
  email: `test${Math.random().toString(36).substr(2, 5)}@example.com`
})

// Async test helpers
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export const waitForElement = async (selector: string, timeout = 5000): Promise<Element | null> => {
  const start = Date.now()
  
  while (Date.now() - start < timeout) {
    const element = document.querySelector(selector)
    if (element) return element
    await waitFor(100)
  }
  
  return null
}

// Error simulation helpers
export const simulateNetworkError = () => {
  throw new Error('Network error. Please check your connection.')
}

export const simulateServerError = () => {
  throw new Error('Server error. Please try again later.')
}

export const simulateValidationError = (field: string) => {
  throw new Error(`Validation failed for field: ${field}`)
}

// Performance testing helpers
export const measurePerformance = async (fn: () => Promise<void> | void, label: string) => {
  const start = performance.now()
  await fn()
  const end = performance.now()
  console.log(`${label} took ${end - start} milliseconds`)
  return end - start
}

// Memory leak detection helpers
export const trackMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    return {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      limit: memory.jsHeapSizeLimit
    }
  }
  return null
}

// Component testing helpers
export const createMockEvent = (type: string, properties: Record<string, any> = {}) => {
  const event = new Event(type, { bubbles: true, cancelable: true })
  Object.assign(event, properties)
  return event
}

export const createMockMouseEvent = (type: string, coordinates: { x: number; y: number }) => {
  return new MouseEvent(type, {
    bubbles: true,
    cancelable: true,
    clientX: coordinates.x,
    clientY: coordinates.y
  })
}

export const createMockKeyboardEvent = (type: string, key: string, modifiers: {
  ctrl?: boolean
  shift?: boolean
  alt?: boolean
  meta?: boolean
} = {}) => {
  return new KeyboardEvent(type, {
    bubbles: true,
    cancelable: true,
    key,
    ctrlKey: modifiers.ctrl || false,
    shiftKey: modifiers.shift || false,
    altKey: modifiers.alt || false,
    metaKey: modifiers.meta || false
  })
}

// Validation helpers
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const validateRequired = (value: any): boolean => {
  return value !== null && value !== undefined && value !== ''
}

export const validateLength = (value: string, min: number, max: number): boolean => {
  return value.length >= min && value.length <= max
}

// Test environment setup
export const setupTestEnvironment = () => {
  // Mock localStorage
  Object.defineProperty(window, 'localStorage', {
    value: mockLocalStorage
  })

  // Mock console methods to avoid noise in tests
  const originalConsole = { ...console }
  console.log = jest.fn()
  console.warn = jest.fn()
  console.error = jest.fn()

  return () => {
    // Cleanup function
    Object.assign(console, originalConsole)
    mockLocalStorage.clear()
  }
}

// API testing helpers
export const createMockApiResponse = <T>(data: T, success = true, message = 'Success') => ({
  success,
  message,
  data
})

export const createMockApiError = (message = 'Error', errors: Record<string, string[]> = {}) => ({
  success: false,
  message,
  errors
})
