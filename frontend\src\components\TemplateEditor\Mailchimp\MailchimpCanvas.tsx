import React, { useRef, useCallback, useState, useEffect } from 'react'
import { useDrop } from 'react-dnd'
import { TemplateComponent, TemplateSettings } from '../../../types/templateEditor'
import { MailchimpComponentRenderer } from './MailchimpComponentRenderer'
import { MailchimpDropZone } from './MailchimpDropZone'

interface MailchimpCanvasProps {
  components: TemplateComponent[]
  selectedComponent: string | null
  onComponentSelect: (id: string | null) => void
  onComponentUpdate: (id: string, updates: Partial<TemplateComponent>) => void
  onComponentDelete: (id: string) => void
  onComponentMove: (fromIndex: number, toIndex: number) => void
  onComponentAdd: (component: TemplateComponent, index?: number) => void
  templateSettings: TemplateSettings
  zoom: number
  showGrid: boolean
  showRulers: boolean
  previewMode: 'desktop' | 'tablet' | 'mobile'
}

export const MailchimpCanvas: React.FC<MailchimpCanvasProps> = ({
  components,
  selectedComponent,
  onComponentSelect,
  onComponentUpdate,
  onComponentDelete,
  onComponentMove,
  onComponentAdd,
  templateSettings,
  zoom,
  showGrid,
  showRulers,
  previewMode
}) => {
  const canvasRef = useRef<HTMLDivElement>(null)
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null)
  const [canvasSize, setCanvasSize] = useState({ width: 600, height: 800 })

  // Update canvas size based on preview mode
  useEffect(() => {
    switch (previewMode) {
      case 'mobile':
        setCanvasSize({ width: 375, height: 667 })
        break
      case 'tablet':
        setCanvasSize({ width: 768, height: 1024 })
        break
      case 'desktop':
      default:
        setCanvasSize({ width: 600, height: 800 })
        break
    }
  }, [previewMode])

  const [{ isOver }, drop] = useDrop(() => ({
    accept: 'component',
    drop: (item: { component: any }, monitor) => {
      if (!monitor.didDrop()) {
        // Add component to the end if dropped on canvas
        const newComponent: TemplateComponent = {
          id: `component-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          type: item.component.componentType,
          content: { ...item.component.defaultContent },
          styles: { ...item.component.defaultStyles },
          position: { x: 0, y: 0 }
        }
        onComponentAdd(newComponent)
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
    }),
  }))

  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    // Only deselect if clicking on the canvas itself, not on components
    if (e.target === e.currentTarget) {
      onComponentSelect(null)
    }
  }, [onComponentSelect])

  const handleComponentClick = useCallback((e: React.MouseEvent, componentId: string) => {
    e.stopPropagation()
    onComponentSelect(componentId)
  }, [onComponentSelect])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (selectedComponent && (e.key === 'Delete' || e.key === 'Backspace')) {
      e.preventDefault()
      onComponentDelete(selectedComponent)
    }
  }, [selectedComponent, onComponentDelete])

  const canvasStyles: React.CSSProperties = {
    width: canvasSize.width,
    minHeight: canvasSize.height,
    backgroundColor: templateSettings.backgroundColor || '#ffffff',
    fontFamily: templateSettings.fontFamily || 'Arial, sans-serif',
    transform: `scale(${zoom})`,
    transformOrigin: 'top center',
    transition: 'transform 0.2s ease-in-out'
  }

  const containerStyles: React.CSSProperties = {
    width: canvasSize.width * zoom,
    height: canvasSize.height * zoom,
    margin: '0 auto'
  }

  return (
    <div className="flex-1 bg-gray-100 overflow-auto">
      {/* Rulers */}
      {showRulers && (
        <>
          {/* Horizontal Ruler */}
          <div className="sticky top-0 z-10 bg-white border-b border-gray-200 h-6">
            <div 
              className="relative mx-auto"
              style={{ width: canvasSize.width * zoom }}
            >
              <svg width="100%" height="24" className="absolute top-0">
                {Array.from({ length: Math.ceil(canvasSize.width / 50) + 1 }, (_, i) => (
                  <g key={i}>
                    <line
                      x1={i * 50 * zoom}
                      y1="16"
                      x2={i * 50 * zoom}
                      y2="24"
                      stroke="#666"
                      strokeWidth="1"
                    />
                    <text
                      x={i * 50 * zoom + 2}
                      y="14"
                      fontSize="10"
                      fill="#666"
                    >
                      {i * 50}
                    </text>
                  </g>
                ))}
              </svg>
            </div>
          </div>

          {/* Vertical Ruler */}
          <div className="fixed left-0 top-24 z-10 bg-white border-r border-gray-200 w-6 h-full">
            <svg width="24" height="100%" className="absolute left-0">
              {Array.from({ length: Math.ceil(canvasSize.height / 50) + 1 }, (_, i) => (
                <g key={i}>
                  <line
                    x1="16"
                    y1={i * 50 * zoom}
                    x2="24"
                    y2={i * 50 * zoom}
                    stroke="#666"
                    strokeWidth="1"
                  />
                  <text
                    x="2"
                    y={i * 50 * zoom + 12}
                    fontSize="10"
                    fill="#666"
                    transform={`rotate(-90, 2, ${i * 50 * zoom + 12})`}
                  >
                    {i * 50}
                  </text>
                </g>
              ))}
            </svg>
          </div>
        </>
      )}

      {/* Canvas Container */}
      <div 
        className="p-8 min-h-full flex justify-center"
        style={{ paddingLeft: showRulers ? '32px' : '32px' }}
      >
        <div style={containerStyles}>
          <div
            ref={(node) => {
              canvasRef.current = node
              drop(node)
            }}
            className={`relative bg-white shadow-lg mx-auto ${
              showGrid ? 'bg-grid-pattern' : ''
            } ${isOver ? 'ring-2 ring-blue-400 ring-opacity-50' : ''}`}
            style={canvasStyles}
            onClick={handleCanvasClick}
            onKeyDown={handleKeyDown}
            tabIndex={0}
          >
            {/* Drop zone at the top */}
            <MailchimpDropZone
              index={0}
              isActive={dragOverIndex === 0}
              onDrop={(component) => onComponentAdd(component, 0)}
              onDragOver={() => setDragOverIndex(0)}
              onDragLeave={() => setDragOverIndex(null)}
            />

            {/* Render components */}
            {components.map((component, index) => (
              <React.Fragment key={component.id}>
                <div
                  className={`relative group ${
                    selectedComponent === component.id
                      ? 'ring-2 ring-blue-500 ring-opacity-75'
                      : 'hover:ring-1 hover:ring-gray-300'
                  }`}
                  onClick={(e) => handleComponentClick(e, component.id)}
                >
                  <MailchimpComponentRenderer
                    component={component}
                    isSelected={selectedComponent === component.id}
                    onUpdate={(updates) => onComponentUpdate(component.id, updates)}
                    onDelete={() => onComponentDelete(component.id)}
                    templateSettings={templateSettings}
                  />

                  {/* Component controls overlay */}
                  {selectedComponent === component.id && (
                    <div className="absolute -top-8 left-0 right-0 flex justify-between items-center bg-blue-500 text-white text-xs px-2 py-1 rounded-t">
                      <span className="font-medium capitalize">{component.type}</span>
                      <div className="flex space-x-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            if (index > 0) {
                              onComponentMove(index, index - 1)
                            }
                          }}
                          className="hover:bg-blue-600 px-1 py-0.5 rounded"
                          title="Move Up"
                          disabled={index === 0}
                        >
                          ↑
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            if (index < components.length - 1) {
                              onComponentMove(index, index + 1)
                            }
                          }}
                          className="hover:bg-blue-600 px-1 py-0.5 rounded"
                          title="Move Down"
                          disabled={index === components.length - 1}
                        >
                          ↓
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            onComponentDelete(component.id)
                          }}
                          className="hover:bg-red-600 px-1 py-0.5 rounded"
                          title="Delete"
                        >
                          ×
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Drop zone between components */}
                <MailchimpDropZone
                  index={index + 1}
                  isActive={dragOverIndex === index + 1}
                  onDrop={(component) => onComponentAdd(component, index + 1)}
                  onDragOver={() => setDragOverIndex(index + 1)}
                  onDragLeave={() => setDragOverIndex(null)}
                />
              </React.Fragment>
            ))}

            {/* Empty state */}
            {components.length === 0 && (
              <div className="flex flex-col items-center justify-center h-96 text-gray-500">
                <div className="text-6xl mb-4">📧</div>
                <h3 className="text-lg font-medium mb-2">Start building your email</h3>
                <p className="text-sm text-center max-w-sm">
                  Drag and drop content blocks from the left panel to start creating your email template.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Grid pattern CSS */}
      <style jsx>{`
        .bg-grid-pattern {
          background-image: 
            linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
          background-size: 20px 20px;
        }
      `}</style>
    </div>
  )
}

export default MailchimpCanvas
