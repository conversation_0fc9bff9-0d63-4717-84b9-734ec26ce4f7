import React, { useState } from 'react'
import { 
  Type, Palette, Layout, Link, Image, 
  Settings, ChevronDown, ChevronRight,
  Bold, Italic, Underline, AlignLeft, 
  AlignCenter, AlignRight, AlignJustify
} from 'lucide-react'
import { TemplateComponent, TemplateSettings } from '../../../types/templateEditor'

interface MailchimpPropertiesPanelProps {
  selectedComponent: TemplateComponent | null
  onComponentUpdate: (id: string, updates: Partial<TemplateComponent>) => void
  templateSettings: TemplateSettings
  onTemplateSettingsUpdate: (settings: Partial<TemplateSettings>) => void
}

export const MailchimpPropertiesPanel: React.FC<MailchimpPropertiesPanelProps> = ({
  selectedComponent,
  onComponentUpdate,
  templateSettings,
  onTemplateSettingsUpdate
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['content', 'style', 'layout'])
  )

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(section)) {
      newExpanded.delete(section)
    } else {
      newExpanded.add(section)
    }
    setExpandedSections(newExpanded)
  }

  const updateComponent = (updates: Partial<TemplateComponent>) => {
    if (selectedComponent) {
      onComponentUpdate(selectedComponent.id, updates)
    }
  }

  const updateContent = (contentUpdates: Record<string, any>) => {
    updateComponent({
      content: { ...selectedComponent?.content, ...contentUpdates }
    })
  }

  const updateStyles = (styleUpdates: Record<string, any>) => {
    updateComponent({
      styles: { ...selectedComponent?.styles, ...styleUpdates }
    })
  }

  const renderContentEditor = () => {
    if (!selectedComponent) return null

    switch (selectedComponent.type) {
      case 'text':
      case 'heading':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Text Content
              </label>
              <textarea
                value={selectedComponent.content.text || ''}
                onChange={(e) => updateContent({ text: e.target.value })}
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={4}
                placeholder="Enter your text here..."
              />
            </div>
            
            {selectedComponent.type === 'heading' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Heading Level
                </label>
                <select
                  value={selectedComponent.content.level || 'h2'}
                  onChange={(e) => updateContent({ level: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="h1">Heading 1</option>
                  <option value="h2">Heading 2</option>
                  <option value="h3">Heading 3</option>
                  <option value="h4">Heading 4</option>
                  <option value="h5">Heading 5</option>
                  <option value="h6">Heading 6</option>
                </select>
              </div>
            )}
          </div>
        )

      case 'image':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Image URL
              </label>
              <input
                type="url"
                value={selectedComponent.content.src || ''}
                onChange={(e) => updateContent({ src: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="https://example.com/image.jpg"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Alt Text
              </label>
              <input
                type="text"
                value={selectedComponent.content.alt || ''}
                onChange={(e) => updateContent({ alt: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Describe the image"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Link URL (optional)
              </label>
              <input
                type="url"
                value={selectedComponent.content.link || ''}
                onChange={(e) => updateContent({ link: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="https://example.com"
              />
            </div>
          </div>
        )

      case 'button':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Button Text
              </label>
              <input
                type="text"
                value={selectedComponent.content.text || ''}
                onChange={(e) => updateContent({ text: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Click Here"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Link URL
              </label>
              <input
                type="url"
                value={selectedComponent.content.href || ''}
                onChange={(e) => updateContent({ href: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="https://example.com"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Target
              </label>
              <select
                value={selectedComponent.content.target || '_blank'}
                onChange={(e) => updateContent({ target: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="_blank">New Window</option>
                <option value="_self">Same Window</option>
              </select>
            </div>
          </div>
        )

      case 'columns':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Number of Columns
              </label>
              <select
                value={selectedComponent.content.columns || 2}
                onChange={(e) => updateContent({ columns: parseInt(e.target.value) })}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={1}>1 Column</option>
                <option value={2}>2 Columns</option>
                <option value={3}>3 Columns</option>
                <option value={4}>4 Columns</option>
              </select>
            </div>
          </div>
        )

      case 'spacer':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Height (px)
              </label>
              <input
                type="number"
                value={parseInt(selectedComponent.styles.height) || 40}
                onChange={(e) => updateStyles({ height: `${e.target.value}px` })}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                min="10"
                max="200"
              />
            </div>
          </div>
        )

      default:
        return (
          <div className="text-center py-8 text-gray-500">
            <p>No content options available for this component type.</p>
          </div>
        )
    }
  }

  const renderStyleEditor = () => {
    if (!selectedComponent) return null

    return (
      <div className="space-y-4">
        {/* Typography */}
        {['text', 'heading', 'button'].includes(selectedComponent.type) && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Font Family
              </label>
              <select
                value={selectedComponent.styles.fontFamily || 'Arial, sans-serif'}
                onChange={(e) => updateStyles({ fontFamily: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="Arial, sans-serif">Arial</option>
                <option value="Helvetica, sans-serif">Helvetica</option>
                <option value="Georgia, serif">Georgia</option>
                <option value="Times New Roman, serif">Times New Roman</option>
                <option value="Courier New, monospace">Courier New</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Font Size
              </label>
              <input
                type="number"
                value={parseInt(selectedComponent.styles.fontSize) || 16}
                onChange={(e) => updateStyles({ fontSize: `${e.target.value}px` })}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                min="8"
                max="72"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Text Color
              </label>
              <input
                type="color"
                value={selectedComponent.styles.color || '#333333'}
                onChange={(e) => updateStyles({ color: e.target.value })}
                className="w-full h-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Text Alignment
              </label>
              <div className="flex space-x-1">
                {[
                  { value: 'left', icon: AlignLeft },
                  { value: 'center', icon: AlignCenter },
                  { value: 'right', icon: AlignRight },
                  { value: 'justify', icon: AlignJustify }
                ].map(({ value, icon: Icon }) => (
                  <button
                    key={value}
                    onClick={() => updateStyles({ textAlign: value })}
                    className={`p-2 border border-gray-300 rounded ${
                      selectedComponent.styles.textAlign === value
                        ? 'bg-blue-100 border-blue-300'
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                  </button>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Background */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Background Color
          </label>
          <input
            type="color"
            value={selectedComponent.styles.backgroundColor || '#ffffff'}
            onChange={(e) => updateStyles({ backgroundColor: e.target.value })}
            className="w-full h-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Spacing */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Padding
          </label>
          <input
            type="number"
            value={parseInt(selectedComponent.styles.padding) || 16}
            onChange={(e) => updateStyles({ padding: `${e.target.value}px` })}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            min="0"
            max="100"
          />
        </div>

        {/* Border */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Border Radius
          </label>
          <input
            type="number"
            value={parseInt(selectedComponent.styles.borderRadius) || 0}
            onChange={(e) => updateStyles({ borderRadius: `${e.target.value}px` })}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            min="0"
            max="50"
          />
        </div>
      </div>
    )
  }

  const renderTemplateSettings = () => {
    return (
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Template Background
          </label>
          <input
            type="color"
            value={templateSettings.backgroundColor || '#ffffff'}
            onChange={(e) => onTemplateSettingsUpdate({ backgroundColor: e.target.value })}
            className="w-full h-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Default Font Family
          </label>
          <select
            value={templateSettings.fontFamily || 'Arial, sans-serif'}
            onChange={(e) => onTemplateSettingsUpdate({ fontFamily: e.target.value })}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="Arial, sans-serif">Arial</option>
            <option value="Helvetica, sans-serif">Helvetica</option>
            <option value="Georgia, serif">Georgia</option>
            <option value="Times New Roman, serif">Times New Roman</option>
            <option value="Courier New, monospace">Courier New</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Template Width (px)
          </label>
          <input
            type="number"
            value={templateSettings.width || 600}
            onChange={(e) => onTemplateSettingsUpdate({ width: parseInt(e.target.value) })}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            min="300"
            max="800"
          />
        </div>
      </div>
    )
  }

  const sections = [
    {
      id: 'content',
      title: 'Content',
      icon: <Type className="w-4 h-4" />,
      content: renderContentEditor()
    },
    {
      id: 'style',
      title: 'Style',
      icon: <Palette className="w-4 h-4" />,
      content: renderStyleEditor()
    },
    {
      id: 'template',
      title: 'Template Settings',
      icon: <Settings className="w-4 h-4" />,
      content: renderTemplateSettings()
    }
  ]

  return (
    <div className="w-80 bg-white border-l border-gray-200 flex flex-col h-full">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">
          {selectedComponent ? 'Edit Component' : 'Template Settings'}
        </h2>
        {selectedComponent && (
          <p className="text-sm text-gray-500 capitalize">
            {selectedComponent.type} Component
          </p>
        )}
      </div>

      <div className="flex-1 overflow-y-auto">
        {selectedComponent ? (
          <div>
            {sections.slice(0, 2).map((section) => {
              const isExpanded = expandedSections.has(section.id)
              
              return (
                <div key={section.id} className="border-b border-gray-100">
                  <button
                    onClick={() => toggleSection(section.id)}
                    className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-2">
                      {section.icon}
                      <span className="font-medium text-gray-900">{section.title}</span>
                    </div>
                    {isExpanded ? (
                      <ChevronDown className="w-4 h-4 text-gray-400" />
                    ) : (
                      <ChevronRight className="w-4 h-4 text-gray-400" />
                    )}
                  </button>
                  
                  {isExpanded && (
                    <div className="px-4 pb-4">
                      {section.content}
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        ) : (
          <div className="p-4">
            <div className="text-center py-8 text-gray-500">
              <Layout className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">No Component Selected</h3>
              <p className="text-sm">
                Select a component from the canvas to edit its properties, or configure template settings below.
              </p>
            </div>
            
            {/* Template Settings */}
            <div className="border-t border-gray-100 pt-4">
              <div className="flex items-center space-x-2 mb-4">
                <Settings className="w-4 h-4" />
                <span className="font-medium text-gray-900">Template Settings</span>
              </div>
              {renderTemplateSettings()}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default MailchimpPropertiesPanel
