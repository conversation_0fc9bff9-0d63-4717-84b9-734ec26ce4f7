import React from 'react'

interface SnapLinesProps {
  type: 'vertical' | 'horizontal'
  position: number
}

export const SnapLines: React.FC<SnapLinesProps> = ({ type, position }) => {
  return (
    <div
      className={`absolute pointer-events-none z-50 ${
        type === 'vertical' 
          ? 'w-px h-full bg-blue-500 border-l border-dashed border-blue-500' 
          : 'h-px w-full bg-blue-500 border-t border-dashed border-blue-500'
      }`}
      style={{
        [type === 'vertical' ? 'left' : 'top']: position,
        opacity: 0.8
      }}
    />
  )
}
