import React, { useState, useReducer, useCallback, useEffect, useMemo } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { ArrowLeft } from 'lucide-react'
import { debounce } from 'lodash'
import axios from 'axios'
import ErrorBoundary from '../components/ErrorBoundary'
import { usePerformanceMonitor, useDebouncedUpdate, useThrottledCallback } from '../hooks/useVirtualization'
import { useKeyboardShortcuts, COMMON_SHORTCUTS, TEMPLATE_EDITOR_SHORTCUTS } from '../hooks/useKeyboardShortcuts'

// Enhanced Components
import { TemplateEditorCanvas } from '../components/TemplateEditor/TemplateEditorCanvas'
import { ComponentLibrary } from '../components/TemplateEditor/ComponentLibrary'
import { PropertiesPanel } from '../components/TemplateEditor/PropertiesPanel'
import { TemplateEditorToolbar } from '../components/TemplateEditor/TemplateEditorToolbar'
import { PreviewModal } from '../components/TemplateEditor/PreviewModal'
import { EmailInputModal } from '../components/EmailInputModal'
import { ConfirmationModal } from '../components/ConfirmationModal'
import { KeyboardShortcutsModal } from '../components/KeyboardShortcutsModal'
import { TemplateSettingsModal } from '../components/TemplateEditor/TemplateSettingsModal'
import { VersionHistoryModal } from '../components/TemplateEditor/VersionHistoryModal'
import LoadingOverlay from '../components/LoadingOverlay'
import { ToastContainer } from 'react-toastify'

// Types
import { TemplateComponent, TemplateEditorState, TemplateEditorAction, TemplateSettings } from '../types/templateEditor'

// API
import { templateEditorApi, TemplateData } from '../services/templateEditorApi'

// Utils
import { parseHtmlToComponents, componentsToHtml } from '../utils/htmlParser'
import { useNotifications } from '../contexts/NotificationContext'

import { htmlToImage } from '../utils/htmlToImage'
import { generateComponentId } from '../utils/idGenerator'

// Styles
import 'react-toastify/dist/ReactToastify.css'

// Constants
const AUTO_SAVE_DELAY = 5000 // 5 seconds
const MAX_HISTORY_STATES = 100
const DEFAULT_TEMPLATE_SETTINGS: TemplateSettings = {
  width: 600,
  backgroundColor: '#ffffff',
  fontFamily: 'Arial, sans-serif',
  contentBackgroundColor: '#ffffff',
  textColor: '#333333',
  linkColor: '#2563eb',
  borderRadius: '4px',
  padding: '20px'
}

// Template Editor Reducer
const templateEditorReducer = (state: TemplateEditorState, action: TemplateEditorAction): TemplateEditorState => {
  switch (action.type) {
    case 'ADD_COMPONENT':
      const newComponent = {
        ...action.payload,
        id: action.payload.id || generateComponentId()
      }
      const newComponents = [...state.components, newComponent]
      return {
        ...state,
        components: newComponents,
        selectedComponent: newComponent.id,
        history: [...state.history.slice(0, state.historyIndex + 1), newComponents].slice(-MAX_HISTORY_STATES),
        historyIndex: Math.min(state.historyIndex + 1, MAX_HISTORY_STATES - 1),
        isDirty: true
      }

    case 'UPDATE_COMPONENT':
      const updatedComponents = state.components.map(comp =>
        comp.id === action.payload.id ? { ...comp, ...action.payload.updates } : comp
      )
      return {
        ...state,
        components: updatedComponents,
        history: [...state.history.slice(0, state.historyIndex + 1), updatedComponents].slice(-MAX_HISTORY_STATES),
        historyIndex: Math.min(state.historyIndex + 1, MAX_HISTORY_STATES - 1),
        isDirty: true
      }

    case 'DELETE_COMPONENT':
      const filteredComponents = state.components.filter(comp => comp.id !== action.payload)
      return {
        ...state,
        components: filteredComponents,
        selectedComponent: state.selectedComponent === action.payload ? null : state.selectedComponent,
        history: [...state.history.slice(0, state.historyIndex + 1), filteredComponents].slice(-MAX_HISTORY_STATES),
        historyIndex: Math.min(state.historyIndex + 1, MAX_HISTORY_STATES - 1),
        isDirty: true
      }

    case 'MOVE_COMPONENT':
      const { fromIndex, toIndex } = action.payload
      const componentsCopy = [...state.components]
      const [movedComponent] = componentsCopy.splice(fromIndex, 1)
      componentsCopy.splice(toIndex, 0, movedComponent)
      return {
        ...state,
        components: componentsCopy,
        history: [...state.history.slice(0, state.historyIndex + 1), componentsCopy].slice(-MAX_HISTORY_STATES),
        historyIndex: Math.min(state.historyIndex + 1, MAX_HISTORY_STATES - 1),
        isDirty: true
      }

    case 'DUPLICATE_COMPONENT':
      const componentToDuplicate = state.components.find(comp => comp.id === action.payload)
      if (!componentToDuplicate) return state
      
      const duplicatedComponent = {
        ...componentToDuplicate,
        id: generateComponentId(),
        // Offset slightly for visibility
        position: {
          x: (componentToDuplicate.position?.x || 0) + 10,
          y: (componentToDuplicate.position?.y || 0) + 10
        }
      }
      return {
        ...state,
        components: [...state.components, duplicatedComponent],
        selectedComponent: duplicatedComponent.id,
        history: [...state.history.slice(0, state.historyIndex + 1), [...state.components, duplicatedComponent]].slice(-MAX_HISTORY_STATES),
        historyIndex: Math.min(state.historyIndex + 1, MAX_HISTORY_STATES - 1),
        isDirty: true
      }

    case 'SELECT_COMPONENT':
      return {
        ...state,
        selectedComponent: action.payload
      }

    case 'SET_ZOOM':
      return {
        ...state,
        zoom: action.payload
      }

    case 'TOGGLE_GRID':
      return {
        ...state,
        showGrid: !state.showGrid
      }

    case 'SET_PREVIEW_MODE':
      return {
        ...state,
        previewMode: action.payload
      }

    case 'UNDO':
      if (state.historyIndex > 0) {
        return {
          ...state,
          components: state.history[state.historyIndex - 1],
          historyIndex: state.historyIndex - 1,
          isDirty: true
        }
      }
      return state

    case 'REDO':
      if (state.historyIndex < state.history.length - 1) {
        return {
          ...state,
          components: state.history[state.historyIndex + 1],
          historyIndex: state.historyIndex + 1,
          isDirty: true
        }
      }
      return state

    case 'SAVE_STATE':
      return {
        ...state,
        isDirty: false
      }

    case 'RESET_COMPONENTS':
      return {
        ...state,
        components: [],
        selectedComponent: null,
        history: [[]],
        historyIndex: 0,
        isDirty: false
      }

    case 'LOAD_COMPONENTS':
      return {
        ...state,
        components: action.payload,
        history: [action.payload],
        historyIndex: 0,
        isDirty: false
      }

    case 'SYNC_FROM_HTML':
      return {
        ...state,
        isDirty: true
      }

    case 'UPDATE_TEMPLATE_SETTINGS':
      return {
        ...state,
        templateSettings: {
          ...state.templateSettings,
          ...action.payload
        },
        isDirty: true
      }

    default:
      return state
  }
}

const TemplateEditor: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const isEditing = Boolean(id)
  const { success, error: showError } = useNotifications()

  // Performance monitoring
  // const performanceMetrics = usePerformanceMonitor('TemplateEditor')

  // State management
  const [state, dispatch] = useReducer(templateEditorReducer, {
    components: [],
    selectedComponent: null,
    history: [[]],
    historyIndex: 0,
    zoom: 1,
    showGrid: true,
    previewMode: 'desktop',
    isDirty: false,
    templateSettings: DEFAULT_TEMPLATE_SETTINGS
  })

  // Template metadata
  const [templateName, setTemplateName] = useState('')
  const [templateDescription, setTemplateDescription] = useState('')
  const [viewMode, setViewMode] = useState<'visual' | 'code'>('visual')
  const [htmlContent, setHtmlContent] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [showEmailModal, setShowEmailModal] = useState(false)
  const [sendingTestEmail, setSendingTestEmail] = useState(false)
  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [showShortcutsModal, setShowShortcutsModal] = useState(false)
  const [showVersionHistory, setShowVersionHistory] = useState(false)
  const [versions, setVersions] = useState<TemplateData[]>([])
  const [showLeaveConfirm, setShowLeaveConfirm] = useState(false)
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(null)

  // Track if template has been saved at least once
  const [templateId, setTemplateId] = useState<string | null>(id || null)
  const [lastSavedState, setLastSavedState] = useState<string>('')
  const [isAutoSaving, setIsAutoSaving] = useState(false)

  // Generate HTML from components
  const generateHTML = useCallback(() => {
    if (viewMode === 'code') {
      return htmlContent
    }
    return templateEditorApi.generateHTML(state.components, state.templateSettings)
  }, [state.components, htmlContent, viewMode, state.templateSettings])

  // Generate a snapshot of the current state for comparison
  const currentStateSnapshot = useMemo(() => JSON.stringify({
    components: state.components,
    name: templateName,
    description: templateDescription,
    html_content: generateHTML(),
    settings: state.templateSettings
  }), [state.components, templateName, templateDescription, state.templateSettings, generateHTML])

  // Check if there are unsaved changes
  const hasUnsavedChanges = useCallback(() => {
    return currentStateSnapshot !== lastSavedState
  }, [currentStateSnapshot, lastSavedState])

  // Load template data if editing
  useEffect(() => {
    const loadTemplate = async () => {
      if (isEditing && id) {
        try {
          setIsLoading(true)
          const template = await templateEditorApi.getTemplate(id)

          setTemplateName(template.name)
          setTemplateDescription(template.description || '')
          setHtmlContent(template.html_content)

          // Load components directly
          if (template.components && Array.isArray(template.components)) {
            dispatch({
              type: 'LOAD_COMPONENTS',
              payload: template.components
            })
          } else {
            dispatch({ type: 'RESET_COMPONENTS' })
          }

          // Load template settings if they exist
          if (template.settings) {
            dispatch({
              type: 'UPDATE_TEMPLATE_SETTINGS',
              payload: template.settings
            })
          }

          // Load version history
          const versions = await templateEditorApi.getTemplateVersions(id)
          setVersions(versions)

          // Mark as clean after loading
          dispatch({ type: 'SAVE_STATE' })
          setLastSavedState(JSON.stringify({
            components: template.components || [],
            name: template.name,
            description: template.description || '',
            html_content: template.html_content,
            settings: template.settings || DEFAULT_TEMPLATE_SETTINGS
          }))
        } catch (error) {
          if (axios.isAxiosError(error)) {
            if (error.response?.status === 404) {
              showError('Template not found', 'It may have been deleted or you don\'t have permission to access it.')
              navigate('/templates')
            } else {
              showError('Failed to load template', error.response?.data?.message || 'An error occurred')
            }
          } else {
            console.error('Failed to load template:', error)
            showError('Failed to load template', 'Please try again later.')
          }
        } finally {
          setIsLoading(false)
        }
      }
    }

    loadTemplate()
  }, [isEditing, id, navigate, showError])

  // Warn user about unsaved changes when leaving the page
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges()) {
        e.preventDefault()
        // Modern browsers ignore the custom message and show their own
        return 'You have unsaved changes. Are you sure you want to leave?'
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasUnsavedChanges])



  // Auto-save functionality with debounce
  useEffect(() => {
    if (!state.isDirty || !templateName.trim()) return

    const autoSave = debounce(async () => {
      try {
        setIsAutoSaving(true)
        const templateData: Omit<TemplateData, 'id' | 'created_at' | 'updated_at'> = {
          name: templateName,
          description: templateDescription,
          html_content: generateHTML(),
          components: state.components,
          settings: state.templateSettings
        }

        if (templateId) {
          // Update existing template
          await templateEditorApi.updateTemplate(templateId, templateData)
          dispatch({ type: 'SAVE_STATE' })
          
          // Refresh version history after save
          if (templateId) {
            const versions = await templateEditorApi.getTemplateVersions(templateId)
            setVersions(versions)
          }
        } else if (templateName.trim()) {
          // Create new template
          const newTemplate = await templateEditorApi.createTemplate(templateData)
          if (newTemplate && newTemplate.id) {
            setTemplateId(newTemplate.id.toString())
            dispatch({ type: 'SAVE_STATE' })
            // Update URL to reflect the new template ID
            window.history.replaceState(null, '', `/templates/edit/${newTemplate.id}`)
          }
        }

        // Update last saved state
        setLastSavedState(JSON.stringify({
          components: state.components,
          name: templateName,
          description: templateDescription,
          html_content: generateHTML(),
          settings: state.templateSettings
        }))
      } catch (error) {
        console.error('Auto-save failed:', error)
      } finally {
        setIsAutoSaving(false)
      }
    }, AUTO_SAVE_DELAY)

    autoSave()

    return () => {
      autoSave.cancel()
    }
  }, [state.isDirty, state.components, templateName, templateDescription, state.templateSettings])

  // Handle HTML content changes in code mode
  const handleHtmlContentChange = useCallback((newHtmlContent: string) => {
    setHtmlContent(newHtmlContent)
    dispatch({ type: 'SYNC_FROM_HTML' })
  }, [])

  // Sync components when switching between modes
  const handleViewModeChange = useCallback((newViewMode: 'visual' | 'code') => {
    if (viewMode === 'code' && newViewMode === 'visual') {
      // Parse HTML content and convert to components
      try {
        if (htmlContent.trim()) {
          const parsedComponents = parseHtmlToComponents(htmlContent)
          if (parsedComponents.length > 0) {
            dispatch({
              type: 'LOAD_COMPONENTS',
              payload: parsedComponents
            })
          }
        }
        dispatch({ type: 'SYNC_FROM_HTML' })
      } catch (error) {
        console.error('Failed to sync from HTML:', error)
        showError('Failed to parse HTML', 'Please check your HTML syntax.')
      }
    } else if (viewMode === 'visual' && newViewMode === 'code') {
      // Convert components to HTML when switching to code mode
      try {
        const generatedHtml = componentsToHtml(state.components, state.templateSettings)
        setHtmlContent(generatedHtml)
      } catch (error) {
        console.error('Failed to generate HTML from components:', error)
      }
    }
    setViewMode(newViewMode)
  }, [viewMode, htmlContent, state.components, state.templateSettings, showError])

  // Handle navigation with unsaved changes check
  const handleNavigation = (path: string) => {
    if (hasUnsavedChanges()) {
      setPendingNavigation(path)
      setShowLeaveConfirm(true)
    } else {
      navigate(path)
    }
  }

  // Confirm navigation when there are unsaved changes
  const confirmNavigation = () => {
    if (pendingNavigation) {
      navigate(pendingNavigation)
    }
    setShowLeaveConfirm(false)
    setPendingNavigation(null)
  }

  // Cancel navigation
  const cancelNavigation = () => {
    setShowLeaveConfirm(false)
    setPendingNavigation(null)
  }

  // Event handlers
  const handleComponentAdd = useCallback((component: TemplateComponent) => {
    dispatch({ type: 'ADD_COMPONENT', payload: component })
  }, [])

  const handleComponentUpdate = useCallback((id: string, updates: Partial<TemplateComponent>) => {
    dispatch({ type: 'UPDATE_COMPONENT', payload: { id, updates } })
  }, [])

  const handleComponentDelete = useCallback((id: string) => {
    dispatch({ type: 'DELETE_COMPONENT', payload: id })
  }, [])

  const handleComponentSelect = useCallback((id: string | null) => {
    dispatch({ type: 'SELECT_COMPONENT', payload: id })
  }, [])



  const handleZoomChange = useThrottledCallback((zoom: number) => {
    dispatch({ type: 'SET_ZOOM', payload: zoom })
  }, 100)

  const handleToggleGrid = useCallback(() => {
    dispatch({ type: 'TOGGLE_GRID' })
  }, [])

  const handlePreviewModeChange = useCallback((mode: 'desktop' | 'tablet' | 'mobile') => {
    dispatch({ type: 'SET_PREVIEW_MODE', payload: mode })
  }, [])

  const handleUndo = useCallback(() => {
    dispatch({ type: 'UNDO' })
  }, [])

  const handleRedo = useCallback(() => {
    dispatch({ type: 'REDO' })
  }, [])

  const handleUpdateTemplateSettings = useCallback((settings: Partial<TemplateSettings>) => {
    dispatch({ type: 'UPDATE_TEMPLATE_SETTINGS', payload: settings })
  }, [])

  const handleSave = async () => {
    if (!templateName.trim()) {
      showError('Validation Error', 'Please enter a template name before saving.')
      return
    }

    try {
      setIsLoading(true)

      const templateData: Omit<TemplateData, 'id' | 'created_at' | 'updated_at'> = {
        name: templateName,
        description: templateDescription,
        html_content: generateHTML(),
        components: state.components,
        settings: state.templateSettings
      }

      if (templateId) {
        // Update existing template
        await templateEditorApi.updateTemplate(templateId, templateData)
        success('Template saved successfully!')
        
        // Refresh version history after save
        const versions = await templateEditorApi.getTemplateVersions(templateId)
        setVersions(versions)
      } else {
        // Create new template
        const newTemplate = await templateEditorApi.createTemplate(templateData)
        if (newTemplate && newTemplate.id) {
          setTemplateId(newTemplate.id.toString())
          // Update URL to reflect the new template ID
          window.history.replaceState(null, '', `/templates/edit/${newTemplate.id}`)
          success('Template created successfully!')
        }
      }

      dispatch({ type: 'SAVE_STATE' })
      setLastSavedState(JSON.stringify({
        components: state.components,
        name: templateName,
        description: templateDescription,
        html_content: generateHTML(),
        settings: state.templateSettings
      }))
    } catch (err) {
      console.error('Failed to save template:', err)
      showError('Failed to save template', 'Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePreview = () => {
    setShowPreview(true)
  }

  const handleExport = async (format: 'html' | 'image' = 'html') => {
    try {
      setIsLoading(true)
      const templateData: TemplateData = {
        name: templateName,
        description: templateDescription,
        html_content: generateHTML(),
        components: state.components,
        settings: state.templateSettings
      }

      if (format === 'html') {
        templateEditorApi.exportTemplate(templateData, 'html')
        success('Template exported successfully!')
      } else {
        // Export as image
        const element = document.getElementById('template-canvas-preview')
        if (element) {
          const dataUrl = await htmlToImage(element)
          const link = document.createElement('a')
          link.download = `${templateName || 'template'}.png`
          link.href = dataUrl
          link.click()
          success('Template exported as image!')
        }
      }
    } catch (err) {
      console.error('Export failed:', err)
      showError('Export failed', 'Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleImport = async (file?: File) => {
    if (!file) {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.html,.json'
      input.onchange = (e) => {
        const selectedFile = (e.target as HTMLInputElement).files?.[0]
        if (selectedFile) handleImport(selectedFile)
      }
      input.click()
      return
    }

    try {
      setIsLoading(true)
      const reader = new FileReader()
      
      reader.onload = async (e) => {
        const content = e.target?.result as string
        
        if (file.name.endsWith('.json')) {
          // Import from JSON (full template data)
          const templateData = JSON.parse(content)
          setTemplateName(templateData.name)
          setTemplateDescription(templateData.description || '')
          
          if (templateData.components && Array.isArray(templateData.components)) {
            dispatch({
              type: 'LOAD_COMPONENTS',
              payload: templateData.components
            })
          }
          
          if (templateData.settings) {
            dispatch({
              type: 'UPDATE_TEMPLATE_SETTINGS',
              payload: templateData.settings
            })
          }
          
          success('Template imported successfully!')
        } else {
          // Import from HTML
          setHtmlContent(content)
          success('HTML content imported! Switch to visual editor to see components.')
        }
      }
      
      reader.readAsText(file)
    } catch (err) {
      console.error('Import failed:', err)
      showError('Import failed', 'Invalid file format or content.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSendTest = () => {
    if (!templateName.trim()) {
      showError('Template not saved', 'Please save the template first before sending a test email.')
      return
    }
    setShowEmailModal(true)
  }

  const handleSendTestEmail = async (email: string) => {
    try {
      setSendingTestEmail(true)
      const templateData: TemplateData = {
        name: templateName,
        description: templateDescription,
        html_content: generateHTML(),
        components: state.components,
        settings: state.templateSettings
      }

      await templateEditorApi.sendTestEmail(templateData, email)
      success('Test email sent successfully', `Email sent to ${email}`)
      setShowEmailModal(false)
    } catch (err: any) {
      console.error('Failed to send test email:', err)
      const errorMessage = err.response?.data?.message || err.message || 'Failed to send test email'
      showError('Error sending test email', errorMessage)
    } finally {
      setSendingTestEmail(false)
    }
  }

  const handleRestoreVersion = async (versionId: string) => {
    try {
      setIsLoading(true)
      const version = await templateEditorApi.getTemplateVersion(versionId)
      
      setTemplateName(version.name)
      setTemplateDescription(version.description || '')
      setHtmlContent(version.html_content)
      
      if (version.components && Array.isArray(version.components)) {
        dispatch({
          type: 'LOAD_COMPONENTS',
          payload: version.components
        })
      }
      
      if (version.settings) {
        dispatch({
          type: 'UPDATE_TEMPLATE_SETTINGS',
          payload: version.settings
        })
      }
      
      success('Version restored successfully!')
      setShowVersionHistory(false)
    } catch (err) {
      console.error('Failed to restore version:', err)
      showError('Failed to restore version', 'Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  // Enhanced keyboard shortcuts
  useKeyboardShortcuts([
    {
      ...COMMON_SHORTCUTS.SAVE,
      callback: handleSave,
      preventDefault: true
    },
    {
      ...COMMON_SHORTCUTS.UNDO,
      callback: handleUndo,
      preventDefault: true
    },
    {
      ...COMMON_SHORTCUTS.REDO,
      callback: handleRedo,
      preventDefault: true
    },
    {
      ...COMMON_SHORTCUTS.DELETE,
      callback: () => {
        if (state.selectedComponent) {
          handleComponentDelete(state.selectedComponent)
        }
      }
    },
    {
      ...TEMPLATE_EDITOR_SHORTCUTS.DUPLICATE,
      callback: () => {
        if (state.selectedComponent) {
          dispatch({ type: 'DUPLICATE_COMPONENT', payload: state.selectedComponent })
        }
      }
    },
    {
      ...TEMPLATE_EDITOR_SHORTCUTS.PREVIEW,
      callback: handlePreview,
      preventDefault: true
    },
    {
      keys: ['ctrl+shift+h', 'cmd+shift+h'],
      callback: () => setShowShortcutsModal(true),
      description: 'Show keyboard shortcuts',
      category: 'Help'
    }
  ])

  const selectedComponentData = state.selectedComponent
    ? state.components.find(c => c.id === state.selectedComponent) || null
    : null

  return (
    <ErrorBoundary>
      <DndProvider backend={HTML5Backend}>
        <div className="h-screen flex flex-col bg-gray-50">
        {/* Notification system */}
        <ToastContainer position="top-right" autoClose={5000} />

        {/* Loading Overlay */}
        <LoadingOverlay isLoading={isLoading} message={isAutoSaving ? "Auto-saving..." : "Loading..."} />

        {/* Back Button */}
        <div className="bg-white border-b px-6 py-2 flex justify-between items-center">
          <button
            onClick={() => handleNavigation('/templates')}
            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Templates
          </button>
          
          <div className="flex items-center space-x-2">
            {isAutoSaving && (
              <span className="text-sm text-gray-500 flex items-center">
                <span className="animate-pulse">Auto-saving...</span>
              </span>
            )}
            <button
              onClick={() => setShowShortcutsModal(true)}
              className="text-sm text-gray-500 hover:text-gray-700"
              title="Keyboard Shortcuts"
            >
              ⌘⇧H
            </button>
          </div>
        </div>

        {/* Toolbar */}
        <TemplateEditorToolbar
          templateName={templateName}
          onTemplateNameChange={setTemplateName}
          viewMode={viewMode}
          onViewModeChange={handleViewModeChange}
          previewMode={state.previewMode}
          onPreviewModeChange={handlePreviewModeChange}
          showGrid={state.showGrid}
          onToggleGrid={handleToggleGrid}
          canUndo={state.historyIndex > 0}
          canRedo={state.historyIndex < state.history.length - 1}
          onUndo={handleUndo}
          onRedo={handleRedo}
          onSave={handleSave}
          onPreview={handlePreview}
          onExport={handleExport}
          onImport={handleImport}
          onSendTest={handleSendTest}
          onShowSettings={() => setShowSettingsModal(true)}
          onShowVersionHistory={() => setShowVersionHistory(true)}
          isDirty={hasUnsavedChanges()}
          zoom={state.zoom}
          onZoomChange={handleZoomChange}
        />

        {/* Editor Content */}
        <div className="flex-1 flex overflow-hidden">
          {viewMode === 'visual' ? (
            <>
              {/* Component Library */}
              <ComponentLibrary
                onComponentAdd={handleComponentAdd}
              />

              {/* Canvas */}
              <TemplateEditorCanvas
                components={state.components}
                selectedComponent={state.selectedComponent}
                onComponentSelect={handleComponentSelect}
                onComponentUpdate={handleComponentUpdate}
                onComponentAdd={handleComponentAdd}
                onComponentDelete={handleComponentDelete}
                zoom={state.zoom}
                showGrid={state.showGrid}
                previewMode={state.previewMode}
              />

              {/* Properties Panel */}
              <PropertiesPanel
                selectedComponent={selectedComponentData}
                onComponentUpdate={(updates) => {
                  if (state.selectedComponent) {
                    handleComponentUpdate(state.selectedComponent, updates)
                  }
                }}
                previewMode={state.previewMode}
                onPreviewModeChange={handlePreviewModeChange}
                templateSettings={state.templateSettings}
                onTemplateSettingsChange={handleUpdateTemplateSettings}
              />
            </>
          ) : (
            /* Code Editor */
            <div className="flex-1 p-6 overflow-auto">
              <div className="h-full">
                <div className="bg-gray-800 text-gray-100 p-2 rounded-t-lg flex justify-between items-center">
                  <span>HTML Editor</span>
                  <button 
                    onClick={() => handleViewModeChange('visual')}
                    className="text-sm bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded"
                  >
                    Switch to Visual Editor
                  </button>
                </div>
                <textarea
                  value={htmlContent}
                  onChange={(e) => handleHtmlContentChange(e.target.value)}
                  className="w-full h-full p-4 border border-gray-300 rounded-b-lg font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your HTML content here..."
                  spellCheck="false"
                />
              </div>
            </div>
          )}
        </div>

        {/* Preview Modal */}
        <PreviewModal
          isOpen={showPreview}
          onClose={() => setShowPreview(false)}
          htmlContent={generateHTML()}
          templateName={templateName}
          previewMode={state.previewMode}
        />

        {/* Email Input Modal */}
        <EmailInputModal
          isOpen={showEmailModal}
          onClose={() => setShowEmailModal(false)}
          onSend={handleSendTestEmail}
          loading={sendingTestEmail}
          title="Send Test Email"
          description="Enter the email address where you want to send a preview of this template."
        />

        {/* Template Settings Modal */}
        <TemplateSettingsModal
          isOpen={showSettingsModal}
          onClose={() => setShowSettingsModal(false)}
          settings={state.templateSettings}
          onSave={handleUpdateTemplateSettings}
        />

        {/* Keyboard Shortcuts Modal */}
        <KeyboardShortcutsModal
          isOpen={showShortcutsModal}
          onClose={() => setShowShortcutsModal(false)}
          shortcuts={[
            { keys: ['Ctrl/Cmd + S'], description: 'Save template' },
            { keys: ['Ctrl/Cmd + Z'], description: 'Undo' },
            { keys: ['Ctrl/Cmd + Y', 'Ctrl/Cmd + Shift + Z'], description: 'Redo' },
            { keys: ['Delete', 'Backspace'], description: 'Delete selected component' },
            { keys: ['Ctrl/Cmd + D'], description: 'Duplicate selected component' },
            { keys: ['Ctrl/Cmd + P'], description: 'Preview template' },
            { keys: ['Ctrl/Cmd + Shift + H'], description: 'Show keyboard shortcuts' }
          ]}
        />

        {/* Version History Modal */}
        <VersionHistoryModal
          isOpen={showVersionHistory}
          onClose={() => setShowVersionHistory(false)}
          versions={versions}
          onRestore={handleRestoreVersion}
          currentVersionId={templateId || ''}
        />

        {/* Leave Confirmation Modal */}
        <ConfirmationModal
          isOpen={showLeaveConfirm}
          onClose={cancelNavigation}
          onConfirm={confirmNavigation}
          title="Unsaved Changes"
          message="You have unsaved changes. Are you sure you want to leave?"
          confirmText="Leave"
          cancelText="Stay"
        />
        </div>
      </DndProvider>
    </ErrorBoundary>
  )
}

export default TemplateEditor