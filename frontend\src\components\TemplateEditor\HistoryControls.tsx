import React from 'react'
import { Undo2, Redo2 } from 'lucide-react'

interface HistoryControlsProps {
  onUndo: () => void
  onRedo: () => void
  canUndo: boolean
  canRedo: boolean
}

export const HistoryControls: React.FC<HistoryControlsProps> = ({
  onUndo,
  onRedo,
  canUndo,
  canRedo
}) => {
  return (
    <div className="flex items-center space-x-1">
      <button
        onClick={onUndo}
        disabled={!canUndo}
        className="p-2 bg-white rounded shadow hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        title="Undo (Ctrl+Z)"
      >
        <Undo2 className="w-4 h-4" />
      </button>
      
      <button
        onClick={onRedo}
        disabled={!canRedo}
        className="p-2 bg-white rounded shadow hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        title="Redo (Ctrl+Y)"
      >
        <Redo2 className="w-4 h-4" />
      </button>
    </div>
  )
}
