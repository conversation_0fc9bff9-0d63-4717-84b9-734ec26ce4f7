interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  version: string
  dependencies?: string[]
}

interface CacheOptions {
  ttl?: number
  version?: string
  dependencies?: string[]
  serialize?: boolean
}

class TemplateCache {
  private cache = new Map<string, CacheEntry<any>>()
  private defaultTTL = 5 * 60 * 1000 // 5 minutes
  private maxSize = 100
  private version = '1.0.0'

  constructor(options: { maxSize?: number; defaultTTL?: number; version?: string } = {}) {
    this.maxSize = options.maxSize || this.maxSize
    this.defaultTTL = options.defaultTTL || this.defaultTTL
    this.version = options.version || this.version

    // Start cleanup interval
    this.startCleanup()
    
    // Load from localStorage on initialization
    this.loadFromStorage()
  }

  set<T>(key: string, data: T, options: CacheOptions = {}): void {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value
      this.cache.delete(oldestKey)
    }

    const entry: CacheEntry<T> = {
      data: options.serialize ? JSON.parse(JSON.stringify(data)) : data,
      timestamp: Date.now(),
      ttl: options.ttl || this.defaultTTL,
      version: options.version || this.version,
      dependencies: options.dependencies
    }

    this.cache.set(key, entry)
    this.saveToStorage(key, entry)
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      return null
    }

    // Check if entry has expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.delete(key)
      return null
    }

    // Check version compatibility
    if (entry.version !== this.version) {
      this.delete(key)
      return null
    }

    return entry.data
  }

  has(key: string): boolean {
    return this.get(key) !== null
  }

  delete(key: string): boolean {
    const deleted = this.cache.delete(key)
    if (deleted) {
      this.removeFromStorage(key)
    }
    return deleted
  }

  clear(): void {
    this.cache.clear()
    this.clearStorage()
  }

  // Invalidate entries by dependency
  invalidateByDependency(dependency: string): void {
    const keysToDelete: string[] = []
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.dependencies?.includes(dependency)) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => this.delete(key))
  }

  // Get cache statistics
  getStats() {
    const now = Date.now()
    let expired = 0
    let valid = 0

    for (const entry of this.cache.values()) {
      if (now > entry.timestamp + entry.ttl) {
        expired++
      } else {
        valid++
      }
    }

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      valid,
      expired,
      hitRate: this.calculateHitRate()
    }
  }

  // Memoization wrapper
  memoize<T extends (...args: any[]) => any>(
    fn: T,
    keyGenerator: (...args: Parameters<T>) => string,
    options: CacheOptions = {}
  ): T {
    return ((...args: Parameters<T>) => {
      const key = keyGenerator(...args)
      const cached = this.get(key)
      
      if (cached !== null) {
        return cached
      }

      const result = fn(...args)
      this.set(key, result, options)
      return result
    }) as T
  }

  // Async memoization
  memoizeAsync<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    keyGenerator: (...args: Parameters<T>) => string,
    options: CacheOptions = {}
  ): T {
    return (async (...args: Parameters<T>) => {
      const key = keyGenerator(...args)
      const cached = this.get(key)
      
      if (cached !== null) {
        return cached
      }

      const result = await fn(...args)
      this.set(key, result, options)
      return result
    }) as T
  }

  // Batch operations
  setMany<T>(entries: Array<{ key: string; data: T; options?: CacheOptions }>): void {
    entries.forEach(({ key, data, options }) => {
      this.set(key, data, options)
    })
  }

  getMany<T>(keys: string[]): Array<{ key: string; data: T | null }> {
    return keys.map(key => ({ key, data: this.get<T>(key) }))
  }

  // Persistence methods
  private saveToStorage(key: string, entry: CacheEntry<any>): void {
    try {
      const storageKey = `template_cache_${key}`
      localStorage.setItem(storageKey, JSON.stringify(entry))
    } catch (error) {
      console.warn('Failed to save cache entry to localStorage:', error)
    }
  }

  private loadFromStorage(): void {
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key?.startsWith('template_cache_')) {
          const cacheKey = key.replace('template_cache_', '')
          const entryData = localStorage.getItem(key)
          
          if (entryData) {
            const entry = JSON.parse(entryData)
            
            // Check if entry is still valid
            if (Date.now() <= entry.timestamp + entry.ttl && entry.version === this.version) {
              this.cache.set(cacheKey, entry)
            } else {
              localStorage.removeItem(key)
            }
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load cache from localStorage:', error)
    }
  }

  private removeFromStorage(key: string): void {
    try {
      localStorage.removeItem(`template_cache_${key}`)
    } catch (error) {
      console.warn('Failed to remove cache entry from localStorage:', error)
    }
  }

  private clearStorage(): void {
    try {
      const keysToRemove: string[] = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key?.startsWith('template_cache_')) {
          keysToRemove.push(key)
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key))
    } catch (error) {
      console.warn('Failed to clear cache from localStorage:', error)
    }
  }

  private startCleanup(): void {
    setInterval(() => {
      const now = Date.now()
      const keysToDelete: string[] = []

      for (const [key, entry] of this.cache.entries()) {
        if (now > entry.timestamp + entry.ttl) {
          keysToDelete.push(key)
        }
      }

      keysToDelete.forEach(key => this.delete(key))
    }, 60000) // Clean up every minute
  }

  private hitCount = 0
  private missCount = 0

  private calculateHitRate(): number {
    const total = this.hitCount + this.missCount
    return total > 0 ? this.hitCount / total : 0
  }

  // Template-specific cache methods
  cacheTemplate(id: string, template: any, options: CacheOptions = {}): void {
    this.set(`template_${id}`, template, {
      ...options,
      dependencies: ['templates', ...(options.dependencies || [])]
    })
  }

  getCachedTemplate(id: string): any | null {
    return this.get(`template_${id}`)
  }

  cacheTemplateList(filters: any, templates: any[], options: CacheOptions = {}): void {
    const key = `templates_${JSON.stringify(filters)}`
    this.set(key, templates, {
      ...options,
      dependencies: ['templates', ...(options.dependencies || [])]
    })
  }

  getCachedTemplateList(filters: any): any[] | null {
    const key = `templates_${JSON.stringify(filters)}`
    return this.get(key)
  }

  invalidateTemplates(): void {
    this.invalidateByDependency('templates')
  }

  // Component cache methods
  cacheComponent(id: string, component: any, options: CacheOptions = {}): void {
    this.set(`component_${id}`, component, {
      ...options,
      dependencies: ['components', ...(options.dependencies || [])]
    })
  }

  getCachedComponent(id: string): any | null {
    return this.get(`component_${id}`)
  }

  invalidateComponents(): void {
    this.invalidateByDependency('components')
  }
}

// Create singleton instance
export const templateCache = new TemplateCache()

// Export class for custom instances
export { TemplateCache }
export type { CacheOptions, CacheEntry }
