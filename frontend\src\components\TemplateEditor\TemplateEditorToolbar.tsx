import React from 'react'
import {
  Save,
  Eye,
  Code,
  Undo,
  Redo,
  Grid3X3,
  Smartphone,
  Monitor,
  Tablet,
  Send,
  Download,
  Upload
} from 'lucide-react'

interface TemplateEditorToolbarProps {
  templateName: string
  onTemplateNameChange: (name: string) => void
  viewMode: 'visual' | 'code'
  onViewModeChange: (mode: 'visual' | 'code') => void
  previewMode: 'desktop' | 'tablet' | 'mobile'
  onPreviewModeChange: (mode: 'desktop' | 'tablet' | 'mobile') => void
  showGrid: boolean
  onToggleGrid: () => void
  canUndo: boolean
  canRedo: boolean
  onUndo: () => void
  onRedo: () => void
  onSave: () => void
  onPreview: () => void
  onExport: () => void
  onImport: () => void
  onSendTest: () => void
  onShowSettings?: () => void
  onShowVersionHistory?: () => void
  isDirty: boolean
  zoom?: number
  onZoomChange?: (zoom: number) => void
}

export const TemplateEditorToolbar: React.FC<TemplateEditorToolbarProps> = ({
  templateName,
  onTemplateNameChange,
  viewMode,
  onViewModeChange,
  previewMode,
  onPreviewModeChange,
  showGrid,
  onToggleGrid,
  canUndo,
  canRedo,
  onUndo,
  onRedo,
  onSave,
  onPreview,
  onExport,
  onImport,
  onSendTest,
  onShowSettings: _onShowSettings,
  onShowVersionHistory: _onShowVersionHistory,
  isDirty,
  zoom: _zoom,
  onZoomChange: _onZoomChange
}) => {
  return (
    <div className="bg-white border-b border-gray-200 px-6 py-3">
      <div className="flex items-center justify-between">
        {/* Left section - Template name and basic controls */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={templateName}
              onChange={(e) => onTemplateNameChange(e.target.value)}
              className="text-lg font-semibold bg-transparent border-none outline-none focus:bg-gray-50 px-2 py-1 rounded"
              placeholder="Untitled Template"
            />
            {isDirty && (
              <span className="w-2 h-2 bg-orange-400 rounded-full" title="Unsaved changes" />
            )}
          </div>

          {/* Undo/Redo */}
          <div className="flex items-center space-x-1">
            <button
              onClick={onUndo}
              disabled={!canUndo}
              className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Undo (Ctrl+Z)"
            >
              <Undo className="w-4 h-4" />
            </button>
            <button
              onClick={onRedo}
              disabled={!canRedo}
              className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Redo (Ctrl+Y)"
            >
              <Redo className="w-4 h-4" />
            </button>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => onViewModeChange('visual')}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                viewMode === 'visual'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Eye className="w-4 h-4 mr-1 inline" />
              Visual
            </button>
            <button
              onClick={() => onViewModeChange('code')}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                viewMode === 'code'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Code className="w-4 h-4 mr-1 inline" />
              Code
            </button>
          </div>
        </div>

        {/* Center section - Preview controls */}
        <div className="flex items-center space-x-3">
          {viewMode === 'visual' && (
            <>
              {/* Device Preview */}
              <div className="flex items-center bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => onPreviewModeChange('desktop')}
                  className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                    previewMode === 'desktop'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="Desktop Preview"
                >
                  <Monitor className="w-4 h-4" />
                </button>
                <button
                  onClick={() => onPreviewModeChange('tablet')}
                  className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                    previewMode === 'tablet'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="Tablet Preview"
                >
                  <Tablet className="w-4 h-4" />
                </button>
                <button
                  onClick={() => onPreviewModeChange('mobile')}
                  className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                    previewMode === 'mobile'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="Mobile Preview"
                >
                  <Smartphone className="w-4 h-4" />
                </button>
              </div>

              {/* Grid Toggle */}
              <button
                onClick={onToggleGrid}
                className={`p-2 rounded transition-colors ${
                  showGrid
                    ? 'bg-blue-100 text-blue-600'
                    : 'hover:bg-gray-100 text-gray-600'
                }`}
                title="Toggle Grid"
              >
                <Grid3X3 className="w-4 h-4" />
              </button>
            </>
          )}
        </div>

        {/* Right section - Actions */}
        <div className="flex items-center space-x-2">
          {/* Import/Export */}
          <div className="flex items-center space-x-1">
            <button
              onClick={onImport}
              className="p-2 rounded hover:bg-gray-100 text-gray-600"
              title="Import Template"
            >
              <Upload className="w-4 h-4" />
            </button>
            <button
              onClick={onExport}
              className="p-2 rounded hover:bg-gray-100 text-gray-600"
              title="Export HTML"
            >
              <Download className="w-4 h-4" />
            </button>
          </div>

          <div className="w-px h-6 bg-gray-300" />

          {/* Test & Preview */}
          <button
            onClick={onSendTest}
            className="px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md flex items-center"
          >
            <Send className="w-4 h-4 mr-1" />
            Send Test
          </button>

          <button
            onClick={onPreview}
            className="px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md flex items-center"
          >
            <Eye className="w-4 h-4 mr-1" />
            Preview
          </button>

          {/* Save */}
          <button
            onClick={onSave}
            className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 flex items-center"
          >
            <Save className="w-4 h-4 mr-1" />
            Save Template
          </button>
        </div>
      </div>
    </div>
  )
}
