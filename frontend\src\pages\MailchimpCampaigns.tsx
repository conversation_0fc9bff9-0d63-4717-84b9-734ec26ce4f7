import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { 
  Plus, Search, Filter, MoreHorizontal, TrendingUp, 
  Users, Mail, Calendar, BarChart3, Eye, Edit, 
  Copy, Trash2, Play, Pause, Clock, CheckCircle,
  AlertCircle, Send, Target, Zap
} from 'lucide-react'
import { MailchimpCampaignDashboard } from '../components/Campaigns/Mailchimp/MailchimpCampaignDashboard'
import { MailchimpCampaignList } from '../components/Campaigns/Mailchimp/MailchimpCampaignList'
import { MailchimpCampaignFilters } from '../components/Campaigns/Mailchimp/MailchimpCampaignFilters'
import { MailchimpCampaignAnalytics } from '../components/Campaigns/Mailchimp/MailchimpCampaignAnalytics'
import { api } from '../services/api'
import { useNotifications } from '../hooks/useNotifications'

interface CampaignStats {
  totalCampaigns: number
  activeCampaigns: number
  totalSent: number
  avgOpenRate: number
  avgClickRate: number
  totalRevenue: number
}

interface Campaign {
  id: number
  name: string
  subject: string
  type: 'regular' | 'ab_test' | 'automated' | 'rss'
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'paused' | 'cancelled'
  template_id: number
  template?: {
    id: number
    name: string
    thumbnail?: string
  }
  recipients: {
    total: number
    lists: string[]
    segments: string[]
  }
  schedule: {
    sendTime?: string
    timezone?: string
    sendTimeOptimization?: boolean
  }
  performance: {
    sent: number
    delivered: number
    opened: number
    clicked: number
    bounced: number
    unsubscribed: number
    openRate: number
    clickRate: number
    bounceRate: number
  }
  created_at: string
  updated_at: string
  sent_at?: string
  scheduled_at?: string
}

const MailchimpCampaigns: React.FC = () => {
  const navigate = useNavigate()
  const { showError, showSuccess } = useNotifications()
  
  // State management
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [stats, setStats] = useState<CampaignStats>({
    totalCampaigns: 0,
    activeCampaigns: 0,
    totalSent: 0,
    avgOpenRate: 0,
    avgClickRate: 0,
    totalRevenue: 0
  })
  const [loading, setLoading] = useState(true)
  const [selectedCampaigns, setSelectedCampaigns] = useState<number[]>([])
  const [viewMode, setViewMode] = useState<'dashboard' | 'list' | 'analytics'>('dashboard')
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState({
    status: 'all',
    type: 'all',
    dateRange: '30days',
    performance: 'all'
  })
  const [sortBy, setSortBy] = useState<'name' | 'created' | 'sent' | 'performance'>('created')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // Load data on component mount
  useEffect(() => {
    loadCampaigns()
    loadStats()
  }, [filters, sortBy, sortOrder])

  const loadCampaigns = async () => {
    try {
      setLoading(true)
      const response = await api.get<Campaign[]>('/campaigns', {
        params: {
          search: searchTerm,
          status: filters.status !== 'all' ? filters.status : undefined,
          type: filters.type !== 'all' ? filters.type : undefined,
          dateRange: filters.dateRange,
          sortBy,
          sortOrder
        }
      })
      setCampaigns(response || [])
    } catch (error) {
      console.error('Failed to load campaigns:', error)
      showError('Failed to load campaigns')
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await api.get<CampaignStats>('/campaigns/stats', {
        params: { dateRange: filters.dateRange }
      })
      setStats(response || stats)
    } catch (error) {
      console.error('Failed to load campaign stats:', error)
    }
  }

  // Campaign actions
  const handleCreateCampaign = (type: 'regular' | 'ab_test' | 'automated' | 'rss' = 'regular') => {
    navigate(`/campaigns/create?type=${type}`)
  }

  const handleEditCampaign = (campaignId: number) => {
    navigate(`/campaigns/edit/${campaignId}`)
  }

  const handleDuplicateCampaign = async (campaignId: number) => {
    try {
      const response = await api.post(`/campaigns/${campaignId}/duplicate`)
      showSuccess('Campaign duplicated successfully')
      loadCampaigns()
    } catch (error) {
      showError('Failed to duplicate campaign')
    }
  }

  const handleDeleteCampaign = async (campaignId: number) => {
    if (!confirm('Are you sure you want to delete this campaign? This action cannot be undone.')) {
      return
    }

    try {
      await api.delete(`/campaigns/${campaignId}`)
      setCampaigns(campaigns.filter(c => c.id !== campaignId))
      showSuccess('Campaign deleted successfully')
    } catch (error) {
      showError('Failed to delete campaign')
    }
  }

  const handlePauseCampaign = async (campaignId: number) => {
    try {
      await api.post(`/campaigns/${campaignId}/pause`)
      loadCampaigns()
      showSuccess('Campaign paused')
    } catch (error) {
      showError('Failed to pause campaign')
    }
  }

  const handleResumeCampaign = async (campaignId: number) => {
    try {
      await api.post(`/campaigns/${campaignId}/resume`)
      loadCampaigns()
      showSuccess('Campaign resumed')
    } catch (error) {
      showError('Failed to resume campaign')
    }
  }

  const handleSendTestEmail = async (campaignId: number, email: string) => {
    try {
      await api.post(`/campaigns/${campaignId}/test`, { email })
      showSuccess('Test email sent successfully')
    } catch (error) {
      showError('Failed to send test email')
    }
  }

  const handleBulkAction = async (action: 'delete' | 'pause' | 'resume', campaignIds: number[]) => {
    try {
      await api.post('/campaigns/bulk-action', { action, campaignIds })
      loadCampaigns()
      setSelectedCampaigns([])
      showSuccess(`${action} completed for ${campaignIds.length} campaigns`)
    } catch (error) {
      showError(`Failed to ${action} campaigns`)
    }
  }

  // Filter and search
  const filteredCampaigns = campaigns.filter(campaign => {
    const matchesSearch = searchTerm === '' || 
      campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      campaign.subject.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = filters.status === 'all' || campaign.status === filters.status
    const matchesType = filters.type === 'all' || campaign.type === filters.type
    
    return matchesSearch && matchesStatus && matchesType
  })

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Campaigns</h1>
              <p className="text-gray-600 mt-1">Create, manage, and track your email campaigns</p>
            </div>
            
            {/* Create Campaign Dropdown */}
            <div className="relative">
              <button
                onClick={() => handleCreateCampaign()}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Campaign
              </button>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="mt-6 border-b border-gray-200">
            <nav className="flex space-x-8">
              {[
                { id: 'dashboard', name: 'Dashboard', icon: BarChart3 },
                { id: 'list', name: 'All Campaigns', icon: Mail },
                { id: 'analytics', name: 'Analytics', icon: TrendingUp }
              ].map(tab => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setViewMode(tab.id as any)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                      viewMode === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4 inline mr-2" />
                    {tab.name}
                  </button>
                )
              })}
            </nav>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {viewMode === 'dashboard' && (
          <MailchimpCampaignDashboard
            stats={stats}
            recentCampaigns={campaigns.slice(0, 5)}
            onCreateCampaign={handleCreateCampaign}
            onViewCampaign={(id) => navigate(`/campaigns/${id}`)}
            loading={loading}
          />
        )}

        {viewMode === 'list' && (
          <div className="space-y-6">
            {/* Search and Filters */}
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4 flex-1">
                  {/* Search */}
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search campaigns..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  {/* Quick Filters */}
                  <div className="flex items-center space-x-2">
                    <select
                      value={filters.status}
                      onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                      className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
                    >
                      <option value="all">All Status</option>
                      <option value="draft">Draft</option>
                      <option value="scheduled">Scheduled</option>
                      <option value="sending">Sending</option>
                      <option value="sent">Sent</option>
                      <option value="paused">Paused</option>
                    </select>

                    <select
                      value={filters.type}
                      onChange={(e) => setFilters({ ...filters, type: e.target.value })}
                      className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
                    >
                      <option value="all">All Types</option>
                      <option value="regular">Regular</option>
                      <option value="ab_test">A/B Test</option>
                      <option value="automated">Automated</option>
                      <option value="rss">RSS</option>
                    </select>
                  </div>
                </div>

                {/* Bulk Actions */}
                {selectedCampaigns.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">
                      {selectedCampaigns.length} selected
                    </span>
                    <button
                      onClick={() => handleBulkAction('delete', selectedCampaigns)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Delete
                    </button>
                  </div>
                )}
              </div>

              <MailchimpCampaignFilters
                filters={filters}
                onFiltersChange={setFilters}
                sortBy={sortBy}
                sortOrder={sortOrder}
                onSortChange={(field, order) => {
                  setSortBy(field)
                  setSortOrder(order)
                }}
              />
            </div>

            {/* Campaign List */}
            <MailchimpCampaignList
              campaigns={filteredCampaigns}
              selectedCampaigns={selectedCampaigns}
              onSelectionChange={setSelectedCampaigns}
              onEdit={handleEditCampaign}
              onDuplicate={handleDuplicateCampaign}
              onDelete={handleDeleteCampaign}
              onPause={handlePauseCampaign}
              onResume={handleResumeCampaign}
              onSendTest={handleSendTestEmail}
              onViewAnalytics={(id) => navigate(`/campaigns/${id}/analytics`)}
              loading={loading}
            />
          </div>
        )}

        {viewMode === 'analytics' && (
          <MailchimpCampaignAnalytics
            campaigns={campaigns}
            stats={stats}
            dateRange={filters.dateRange}
            onDateRangeChange={(range) => setFilters({ ...filters, dateRange: range })}
          />
        )}
      </div>
    </div>
  )
}

export default MailchimpCampaigns
