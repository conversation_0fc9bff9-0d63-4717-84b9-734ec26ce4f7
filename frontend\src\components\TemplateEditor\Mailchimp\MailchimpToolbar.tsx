import React, { useState } from 'react'
import { 
  Save, Eye, Send, Settings, Undo, Redo, 
  Smartphone, Monitor, Tablet, ZoomIn, ZoomOut, 
  Grid, Ruler, MoreHorizontal, ArrowLeft,
  Download, Upload, Copy, Trash2, History
} from 'lucide-react'

interface MailchimpToolbarProps {
  templateName: string
  onTemplateNameChange: (name: string) => void
  onSave: () => void
  onPreview: () => void
  onSendTest: () => void
  onSettings: () => void
  onUndo: () => void
  onRedo: () => void
  onBack: () => void
  canUndo: boolean
  canRedo: boolean
  isSaving: boolean
  isDirty: boolean
  zoom: number
  onZoomChange: (zoom: number) => void
  previewMode: 'desktop' | 'tablet' | 'mobile'
  onPreviewModeChange: (mode: 'desktop' | 'tablet' | 'mobile') => void
  showGrid: boolean
  onToggleGrid: () => void
  showRulers: boolean
  onToggleRulers: () => void
  onVersionHistory?: () => void
  onExport?: () => void
  onImport?: () => void
  onDuplicate?: () => void
  onDelete?: () => void
}

export const MailchimpToolbar: React.FC<MailchimpToolbarProps> = ({
  templateName,
  onTemplateNameChange,
  onSave,
  onPreview,
  onSendTest,
  onSettings,
  onUndo,
  onRedo,
  onBack,
  canUndo,
  canRedo,
  isSaving,
  isDirty,
  zoom,
  onZoomChange,
  previewMode,
  onPreviewModeChange,
  showGrid,
  onToggleGrid,
  showRulers,
  onToggleRulers,
  onVersionHistory,
  onExport,
  onImport,
  onDuplicate,
  onDelete
}) => {
  const [showMoreMenu, setShowMoreMenu] = useState(false)
  const [isEditingName, setIsEditingName] = useState(false)

  const handleNameSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsEditingName(false)
  }

  const handleNameKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      setIsEditingName(false)
    } else if (e.key === 'Escape') {
      setIsEditingName(false)
    }
  }

  const zoomLevels = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 2]
  const currentZoomIndex = zoomLevels.findIndex(level => level === zoom)

  return (
    <div className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        {/* Left Section - Navigation & Template Name */}
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            title="Back to Templates"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            <span className="hidden sm:inline">Templates</span>
          </button>

          <div className="h-6 w-px bg-gray-300"></div>

          <div className="flex items-center space-x-2">
            {isEditingName ? (
              <form onSubmit={handleNameSubmit} className="flex items-center">
                <input
                  type="text"
                  value={templateName}
                  onChange={(e) => onTemplateNameChange(e.target.value)}
                  onBlur={() => setIsEditingName(false)}
                  onKeyDown={handleNameKeyDown}
                  className="text-lg font-semibold text-gray-900 bg-transparent border-b-2 border-blue-500 focus:outline-none min-w-0"
                  autoFocus
                />
              </form>
            ) : (
              <button
                onClick={() => setIsEditingName(true)}
                className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors"
                title="Click to edit template name"
              >
                {templateName || 'Untitled Template'}
              </button>
            )}
            
            {isDirty && (
              <div className="flex items-center text-amber-600">
                <div className="w-2 h-2 bg-amber-500 rounded-full mr-2"></div>
                <span className="text-sm hidden sm:inline">Unsaved</span>
              </div>
            )}
          </div>
        </div>

        {/* Center Section - Preview Mode & Zoom */}
        <div className="flex items-center space-x-4">
          {/* Preview Mode Selector */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => onPreviewModeChange('desktop')}
              className={`p-2 rounded-md transition-colors ${
                previewMode === 'desktop'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="Desktop View"
            >
              <Monitor className="w-4 h-4" />
            </button>
            <button
              onClick={() => onPreviewModeChange('tablet')}
              className={`p-2 rounded-md transition-colors ${
                previewMode === 'tablet'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="Tablet View"
            >
              <Tablet className="w-4 h-4" />
            </button>
            <button
              onClick={() => onPreviewModeChange('mobile')}
              className={`p-2 rounded-md transition-colors ${
                previewMode === 'mobile'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="Mobile View"
            >
              <Smartphone className="w-4 h-4" />
            </button>
          </div>

          {/* Zoom Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onZoomChange(zoomLevels[Math.max(0, currentZoomIndex - 1)])}
              disabled={currentZoomIndex <= 0}
              className="p-2 text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Zoom Out"
            >
              <ZoomOut className="w-4 h-4" />
            </button>
            <span className="text-sm text-gray-700 min-w-[60px] text-center">
              {Math.round(zoom * 100)}%
            </span>
            <button
              onClick={() => onZoomChange(zoomLevels[Math.min(zoomLevels.length - 1, currentZoomIndex + 1)])}
              disabled={currentZoomIndex >= zoomLevels.length - 1}
              className="p-2 text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Zoom In"
            >
              <ZoomIn className="w-4 h-4" />
            </button>
          </div>

          {/* View Options */}
          <div className="flex items-center space-x-1">
            <button
              onClick={onToggleGrid}
              className={`p-2 rounded-md transition-colors ${
                showGrid
                  ? 'bg-blue-100 text-blue-600'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="Toggle Grid"
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={onToggleRulers}
              className={`p-2 rounded-md transition-colors ${
                showRulers
                  ? 'bg-blue-100 text-blue-600'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              title="Toggle Rulers"
            >
              <Ruler className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Right Section - Actions */}
        <div className="flex items-center space-x-2">
          {/* Undo/Redo */}
          <div className="flex items-center space-x-1">
            <button
              onClick={onUndo}
              disabled={!canUndo}
              className="p-2 text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Undo"
            >
              <Undo className="w-4 h-4" />
            </button>
            <button
              onClick={onRedo}
              disabled={!canRedo}
              className="p-2 text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Redo"
            >
              <Redo className="w-4 h-4" />
            </button>
          </div>

          <div className="h-6 w-px bg-gray-300"></div>

          {/* Primary Actions */}
          <button
            onClick={onPreview}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            <Eye className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Preview</span>
          </button>

          <button
            onClick={onSendTest}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            <Send className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Send Test</span>
          </button>

          <button
            onClick={onSave}
            disabled={isSaving || !isDirty}
            className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Save className="w-4 h-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save'}
          </button>

          {/* More Menu */}
          <div className="relative">
            <button
              onClick={() => setShowMoreMenu(!showMoreMenu)}
              className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
              title="More Options"
            >
              <MoreHorizontal className="w-4 h-4" />
            </button>

            {showMoreMenu && (
              <div className="absolute right-0 top-full mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                <div className="py-1">
                  <button
                    onClick={() => {
                      onSettings()
                      setShowMoreMenu(false)
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Settings className="w-4 h-4 mr-3" />
                    Template Settings
                  </button>
                  
                  {onVersionHistory && (
                    <button
                      onClick={() => {
                        onVersionHistory()
                        setShowMoreMenu(false)
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <History className="w-4 h-4 mr-3" />
                      Version History
                    </button>
                  )}

                  <div className="border-t border-gray-100 my-1"></div>

                  {onDuplicate && (
                    <button
                      onClick={() => {
                        onDuplicate()
                        setShowMoreMenu(false)
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Copy className="w-4 h-4 mr-3" />
                      Duplicate Template
                    </button>
                  )}

                  {onExport && (
                    <button
                      onClick={() => {
                        onExport()
                        setShowMoreMenu(false)
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Download className="w-4 h-4 mr-3" />
                      Export Template
                    </button>
                  )}

                  {onImport && (
                    <button
                      onClick={() => {
                        onImport()
                        setShowMoreMenu(false)
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <Upload className="w-4 h-4 mr-3" />
                      Import Template
                    </button>
                  )}

                  {onDelete && (
                    <>
                      <div className="border-t border-gray-100 my-1"></div>
                      <button
                        onClick={() => {
                          onDelete()
                          setShowMoreMenu(false)
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4 mr-3" />
                        Delete Template
                      </button>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Click outside to close more menu */}
      {showMoreMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowMoreMenu(false)}
        />
      )}
    </div>
  )
}

export default MailchimpToolbar
