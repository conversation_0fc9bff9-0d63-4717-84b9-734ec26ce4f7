import React, { useState, useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { ArrowLeft, ArrowRight, Save, Send, Check } from 'lucide-react'
import { MailchimpCampaignSetup } from '../components/Campaigns/Mailchimp/MailchimpCampaignSetup'
import { MailchimpRecipientSelector } from '../components/Campaigns/Mailchimp/MailchimpRecipientSelector'
import { MailchimpTemplateSelector } from '../components/Campaigns/Mailchimp/MailchimpTemplateSelector'
import { MailchimpCampaignConfirm } from '../components/Campaigns/Mailchimp/MailchimpCampaignConfirm'
import { api } from '../services/api'
import { useNotifications } from '../hooks/useNotifications'

interface CampaignData {
  // Basic Info
  name: string
  subject: string
  preheader: string
  fromName: string
  fromEmail: string
  replyTo: string
  
  // Campaign Type
  type: 'regular' | 'ab_test' | 'automated' | 'rss'
  
  // A/B Test specific
  abTest?: {
    testType: 'subject' | 'from_name' | 'content'
    testPercentage: number
    winnerCriteria: 'opens' | 'clicks'
    testDuration: number
  }
  
  // Recipients
  recipients: {
    lists: number[]
    segments: number[]
    tags: string[]
    totalCount: number
  }
  
  // Template
  template: {
    id?: number
    type: 'existing' | 'new' | 'gallery'
    content?: any
  }
  
  // Scheduling
  schedule: {
    sendImmediately: boolean
    sendTime?: string
    timezone: string
    sendTimeOptimization: boolean
  }
  
  // Tracking
  tracking: {
    opens: boolean
    clicks: boolean
    googleAnalytics: boolean
    facebookPixel: boolean
  }
}

const MailchimpCampaignCreator: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { showError, showSuccess } = useNotifications()
  
  const campaignType = (searchParams.get('type') as 'regular' | 'ab_test' | 'automated' | 'rss') || 'regular'
  
  const [currentStep, setCurrentStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [campaignData, setCampaignData] = useState<CampaignData>({
    name: '',
    subject: '',
    preheader: '',
    fromName: '',
    fromEmail: '',
    replyTo: '',
    type: campaignType,
    recipients: {
      lists: [],
      segments: [],
      tags: [],
      totalCount: 0
    },
    template: {
      type: 'existing'
    },
    schedule: {
      sendImmediately: true,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      sendTimeOptimization: false
    },
    tracking: {
      opens: true,
      clicks: true,
      googleAnalytics: false,
      facebookPixel: false
    }
  })

  const steps = [
    { 
      id: 1, 
      name: 'Setup', 
      description: 'Campaign details and settings',
      component: MailchimpCampaignSetup
    },
    { 
      id: 2, 
      name: 'Recipients', 
      description: 'Choose your audience',
      component: MailchimpRecipientSelector
    },
    { 
      id: 3, 
      name: 'Template', 
      description: 'Design your email',
      component: MailchimpTemplateSelector
    },
    { 
      id: 4, 
      name: 'Confirm', 
      description: 'Review and send',
      component: MailchimpCampaignConfirm
    }
  ]

  const currentStepData = steps[currentStep - 1]
  const CurrentStepComponent = currentStepData.component

  // Validation for each step
  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(campaignData.name && campaignData.subject && campaignData.fromName && campaignData.fromEmail)
      case 2:
        return campaignData.recipients.totalCount > 0
      case 3:
        return !!(campaignData.template.id || campaignData.template.content)
      case 4:
        return true
      default:
        return false
    }
  }

  const canProceed = validateStep(currentStep)

  const handleNext = () => {
    if (canProceed && currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSaveDraft = async () => {
    try {
      setLoading(true)
      
      const payload = {
        ...campaignData,
        status: 'draft'
      }

      const response = await api.post('/campaigns', payload)
      showSuccess('Campaign saved as draft')
      navigate('/campaigns')
    } catch (error) {
      console.error('Failed to save campaign:', error)
      showError('Failed to save campaign')
    } finally {
      setLoading(false)
    }
  }

  const handleSendCampaign = async () => {
    try {
      setLoading(true)
      
      const payload = {
        ...campaignData,
        status: campaignData.schedule.sendImmediately ? 'sending' : 'scheduled'
      }

      const response = await api.post('/campaigns', payload)
      
      if (campaignData.schedule.sendImmediately) {
        await api.post(`/campaigns/${response.id}/send`)
        showSuccess('Campaign sent successfully!')
      } else {
        showSuccess('Campaign scheduled successfully!')
      }
      
      navigate('/campaigns')
    } catch (error) {
      console.error('Failed to send campaign:', error)
      showError('Failed to send campaign')
    } finally {
      setLoading(false)
    }
  }

  const updateCampaignData = (updates: Partial<CampaignData>) => {
    setCampaignData(prev => ({ ...prev, ...updates }))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/campaigns')}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Create {campaignType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} Campaign
                </h1>
                <p className="text-sm text-gray-600">
                  Step {currentStep} of {steps.length}: {currentStepData.description}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={handleSaveDraft}
                disabled={loading}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Save className="w-4 h-4 mr-2 inline" />
                Save Draft
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className="flex items-center">
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors ${
                    currentStep > step.id
                      ? 'bg-green-500 border-green-500 text-white'
                      : currentStep === step.id
                      ? 'bg-blue-500 border-blue-500 text-white'
                      : 'bg-white border-gray-300 text-gray-500'
                  }`}
                >
                  {currentStep > step.id ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <span className="text-sm font-medium">{step.id}</span>
                  )}
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-gray-900' : 'text-gray-500'
                  }`}>
                    {step.name}
                  </p>
                </div>
              </div>
              
              {index < steps.length - 1 && (
                <div className={`w-16 h-0.5 mx-4 transition-colors ${
                  currentStep > step.id ? 'bg-green-500' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="flex-1 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <CurrentStepComponent
              campaignData={campaignData}
              onUpdate={updateCampaignData}
              onNext={handleNext}
              onPrevious={handlePrevious}
              canProceed={canProceed}
              isLastStep={currentStep === steps.length}
              loading={loading}
            />
          </div>
        </div>
      </div>

      {/* Navigation Footer */}
      <div className="bg-white border-t border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className="flex items-center px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Previous
          </button>

          <div className="flex items-center space-x-3">
            {currentStep === steps.length ? (
              <>
                <button
                  onClick={handleSaveDraft}
                  disabled={loading}
                  className="px-6 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Save Draft
                </button>
                <button
                  onClick={handleSendCampaign}
                  disabled={loading || !canProceed}
                  className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Send className="w-4 h-4 mr-2" />
                  {loading 
                    ? 'Processing...' 
                    : campaignData.schedule.sendImmediately 
                      ? 'Send Campaign' 
                      : 'Schedule Campaign'
                  }
                </button>
              </>
            ) : (
              <button
                onClick={handleNext}
                disabled={!canProceed}
                className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Next
                <ArrowRight className="w-4 h-4 ml-2" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default MailchimpCampaignCreator
