<?php

namespace App\Controllers;

class AuthController extends BaseController
{
    public function login(): void
    {
        $data = $this->getJsonInput();

        // Rate limiting check
        $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        if (!$this->rateLimitCheck("login_{$clientIp}", 5, 900)) { // 5 attempts per 15 minutes
            $this->errorResponse('Too many login attempts. Please try again later.', 429);
            return;
        }

        $errors = $this->validateRequired($data, ['email', 'password']);
        if (!empty($errors)) {
            $this->errorResponse('Validation failed', 422, $errors);
            return;
        }

        // Sanitize inputs
        $email = $this->sanitizeInput($data['email']);
        $password = $data['password'];

        if (!$this->validateEmail($email)) {
            $this->errorResponse('Invalid email format', 422, [
                'email' => ['Please provide a valid email address']
            ]);
            return;
        }

        if (!$this->validateLength($password, 1, 255)) {
            $this->errorResponse('Invalid password length', 422, [
                'password' => ['Password is required']
            ]);
            return;
        }

        try {
            $stmt = $this->db->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch();

            if (!$user || !password_verify($password, $user['password'])) {
                // Log failed login attempt
                error_log("Failed login attempt for email: {$email} from IP: {$clientIp}");
                $this->errorResponse('Invalid credentials', 401);
                return;
            }

            $token = $this->generateToken($user);

            // Remove password from response
            unset($user['password']);

            // Log successful login
            error_log("Successful login for user ID: {$user['id']} from IP: {$clientIp}");

            $this->successResponse([
                'user' => $user,
                'token' => $token
            ], 'Login successful');

        } catch (\Exception $e) {
            error_log("Login error: " . $e->getMessage());
            $this->errorResponse('Login failed', 500);
        }
    }
    
    public function register(): void
    {
        $data = $this->getJsonInput();

        // Rate limiting check
        $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        if (!$this->rateLimitCheck("register_{$clientIp}", 3, 3600)) { // 3 attempts per hour
            $this->errorResponse('Too many registration attempts. Please try again later.', 429);
            return;
        }

        $errors = $this->validateRequired($data, ['name', 'email', 'password', 'confirmPassword']);
        if (!empty($errors)) {
            $this->errorResponse('Validation failed', 422, $errors);
            return;
        }

        // Sanitize inputs
        $name = $this->sanitizeInput($data['name']);
        $email = $this->sanitizeInput($data['email']);
        $password = $data['password'];
        $confirmPassword = $data['confirmPassword'];

        // Validate name
        if (!$this->validateLength($name, 2, 100)) {
            $errors['name'] = ['Name must be between 2 and 100 characters'];
        }

        // Validate email
        if (!$this->validateEmail($email)) {
            $errors['email'] = ['Please provide a valid email address'];
        }

        // Validate password strength
        if (!$this->validateLength($password, 8, 255)) {
            $errors['password'] = ['Password must be at least 8 characters long'];
        } elseif (!$this->validatePasswordStrength($password)) {
            $errors['password'] = ['Password must contain at least one uppercase letter, one lowercase letter, and one number'];
        }

        // Confirm password match
        if ($password !== $confirmPassword) {
            $errors['confirmPassword'] = ['Passwords do not match'];
        }

        if (!empty($errors)) {
            $this->errorResponse('Validation failed', 422, $errors);
            return;
        }
        
        try {
            // Check if email already exists
            $stmt = $this->db->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$data['email']]);
            
            if ($stmt->fetch()) {
                $this->errorResponse('Email already registered', 422, [
                    'email' => ['This email is already registered']
                ]);
                return;
            }
            
            // Create user
            $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
            
            $stmt = $this->db->prepare("
                INSERT INTO users (name, email, password) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$data['name'], $data['email'], $hashedPassword]);
            
            $userId = $this->db->lastInsertId();
            
            // Get the created user
            $stmt = $this->db->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            $token = $this->generateToken($user);
            
            // Remove password from response
            unset($user['password']);
            
            $this->successResponse([
                'user' => $user,
                'token' => $token
            ], 'Registration successful');
            
        } catch (\Exception $e) {
            $this->errorResponse('Registration failed', 500);
        }
    }
    
    public function verify(): void
    {
        $user = $this->getCurrentUser();
        
        if (!$user) {
            $this->errorResponse('Invalid token', 401);
            return;
        }
        
        // Remove password from response
        unset($user['password']);
        
        $this->successResponse($user, 'Token verified');
    }
    
    public function logout(): void
    {
        // In a real application, you might want to blacklist the token
        $this->successResponse(null, 'Logged out successfully');
    }
}
