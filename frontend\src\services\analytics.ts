interface AnalyticsEvent {
  name: string
  properties?: Record<string, any>
  timestamp?: number
  userId?: string
  sessionId?: string
}

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  metadata?: Record<string, any>
}

interface ErrorEvent {
  message: string
  stack?: string
  timestamp: number
  userId?: string
  sessionId?: string
  url: string
  userAgent: string
  metadata?: Record<string, any>
}

class AnalyticsService {
  private events: AnalyticsEvent[] = []
  private performanceMetrics: PerformanceMetric[] = []
  private errors: ErrorEvent[] = []
  private sessionId: string
  private userId?: string
  private isEnabled: boolean = true
  private batchSize: number = 10
  private flushInterval: number = 30000 // 30 seconds

  constructor() {
    this.sessionId = this.generateSessionId()
    this.setupErrorTracking()
    this.setupPerformanceTracking()
    this.startBatchFlush()
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  setUserId(userId: string): void {
    this.userId = userId
  }

  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
  }

  // Event tracking
  track(eventName: string, properties?: Record<string, any>): void {
    if (!this.isEnabled) return

    const event: AnalyticsEvent = {
      name: eventName,
      properties,
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId
    }

    this.events.push(event)
    this.checkBatchFlush()

    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Analytics Event:', event)
    }
  }

  // Performance tracking
  trackPerformance(metricName: string, value: number, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return

    const metric: PerformanceMetric = {
      name: metricName,
      value,
      timestamp: Date.now(),
      metadata
    }

    this.performanceMetrics.push(metric)
    this.checkBatchFlush()
  }

  // Error tracking
  trackError(error: Error | string, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return

    const errorEvent: ErrorEvent = {
      message: typeof error === 'string' ? error : error.message,
      stack: typeof error === 'object' ? error.stack : undefined,
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId,
      url: window.location.href,
      userAgent: navigator.userAgent,
      metadata
    }

    this.errors.push(errorEvent)
    this.checkBatchFlush()

    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Analytics Error:', errorEvent)
    }
  }

  // Template Editor specific events
  trackTemplateAction(action: string, templateId?: string, metadata?: Record<string, any>): void {
    this.track('template_action', {
      action,
      templateId,
      ...metadata
    })
  }

  trackComponentAction(action: string, componentType?: string, metadata?: Record<string, any>): void {
    this.track('component_action', {
      action,
      componentType,
      ...metadata
    })
  }

  trackUserInteraction(interaction: string, element?: string, metadata?: Record<string, any>): void {
    this.track('user_interaction', {
      interaction,
      element,
      ...metadata
    })
  }

  // Page tracking
  trackPageView(page: string, metadata?: Record<string, any>): void {
    this.track('page_view', {
      page,
      url: window.location.href,
      referrer: document.referrer,
      ...metadata
    })
  }

  // Timing utilities
  startTiming(name: string): () => void {
    const startTime = performance.now()
    
    return () => {
      const duration = performance.now() - startTime
      this.trackPerformance(name, duration, { unit: 'milliseconds' })
    }
  }

  // Measure function execution time
  measureFunction<T extends (...args: any[]) => any>(
    fn: T,
    name: string
  ): T {
    return ((...args: Parameters<T>) => {
      const endTiming = this.startTiming(name)
      try {
        const result = fn(...args)
        
        // Handle async functions
        if (result instanceof Promise) {
          return result.finally(() => endTiming())
        }
        
        endTiming()
        return result
      } catch (error) {
        endTiming()
        this.trackError(error as Error, { function: name })
        throw error
      }
    }) as T
  }

  // Batch processing
  private checkBatchFlush(): void {
    const totalEvents = this.events.length + this.performanceMetrics.length + this.errors.length
    if (totalEvents >= this.batchSize) {
      this.flush()
    }
  }

  private startBatchFlush(): void {
    setInterval(() => {
      this.flush()
    }, this.flushInterval)
  }

  private async flush(): Promise<void> {
    if (this.events.length === 0 && this.performanceMetrics.length === 0 && this.errors.length === 0) {
      return
    }

    const batch = {
      events: [...this.events],
      performanceMetrics: [...this.performanceMetrics],
      errors: [...this.errors],
      sessionId: this.sessionId,
      userId: this.userId,
      timestamp: Date.now()
    }

    // Clear the arrays
    this.events = []
    this.performanceMetrics = []
    this.errors = []

    try {
      // In a real application, send to analytics service
      await this.sendBatch(batch)
    } catch (error) {
      console.error('Failed to send analytics batch:', error)
      
      // Re-add events to queue for retry (with limit to prevent infinite growth)
      if (this.events.length < 100) {
        this.events.unshift(...batch.events.slice(-50))
        this.performanceMetrics.unshift(...batch.performanceMetrics.slice(-50))
        this.errors.unshift(...batch.errors.slice(-50))
      }
    }
  }

  private async sendBatch(batch: any): Promise<void> {
    // In development, just log the batch
    if (process.env.NODE_ENV === 'development') {
      console.log('Analytics Batch:', batch)
      return
    }

    // In production, send to your analytics service
    // Example:
    // await fetch('/api/analytics', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(batch)
    // })
  }

  // Error tracking setup
  private setupErrorTracking(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.trackError(event.error || event.message, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        type: 'javascript_error'
      })
    })

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.trackError(event.reason, {
        type: 'unhandled_promise_rejection'
      })
    })
  }

  // Performance tracking setup
  private setupPerformanceTracking(): void {
    // Track page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        
        if (navigation) {
          this.trackPerformance('page_load_time', navigation.loadEventEnd - navigation.fetchStart)
          this.trackPerformance('dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.fetchStart)
          this.trackPerformance('first_paint', navigation.responseEnd - navigation.fetchStart)
        }
      }, 0)
    })

    // Track resource loading
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming
          this.trackPerformance('resource_load_time', resourceEntry.duration, {
            resource: resourceEntry.name,
            type: resourceEntry.initiatorType
          })
        }
      }
    })

    observer.observe({ entryTypes: ['resource'] })
  }

  // Get analytics summary
  getSummary() {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      eventsQueued: this.events.length,
      metricsQueued: this.performanceMetrics.length,
      errorsQueued: this.errors.length,
      isEnabled: this.isEnabled
    }
  }

  // Manual flush
  async forceFlush(): Promise<void> {
    await this.flush()
  }
}

// Create singleton instance
export const analytics = new AnalyticsService()

// Template Editor specific tracking helpers
export const trackTemplateEditor = {
  componentAdded: (componentType: string, method: 'drag' | 'click') => {
    analytics.trackComponentAction('added', componentType, { method })
  },
  
  componentDeleted: (componentType: string) => {
    analytics.trackComponentAction('deleted', componentType)
  },
  
  componentUpdated: (componentType: string, property: string) => {
    analytics.trackComponentAction('updated', componentType, { property })
  },
  
  templateSaved: (templateId: string, isNew: boolean) => {
    analytics.trackTemplateAction('saved', templateId, { isNew })
  },
  
  templateLoaded: (templateId: string, loadTime: number) => {
    analytics.trackTemplateAction('loaded', templateId)
    analytics.trackPerformance('template_load_time', loadTime, { templateId })
  },
  
  previewOpened: (templateId: string) => {
    analytics.trackTemplateAction('preview_opened', templateId)
  },
  
  exportCompleted: (templateId: string, format: string) => {
    analytics.trackTemplateAction('exported', templateId, { format })
  }
}

export { AnalyticsService }
export type { AnalyticsEvent, PerformanceMetric, ErrorEvent }
