import DOMPurify from 'dompurify'

interface SecurityConfig {
  csp: {
    enabled: boolean
    directives: Record<string, string[]>
  }
  xss: {
    enabled: boolean
    allowedTags: string[]
    allowedAttributes: Record<string, string[]>
  }
  csrf: {
    enabled: boolean
    tokenName: string
  }
  rateLimit: {
    enabled: boolean
    maxRequests: number
    windowMs: number
  }
}

interface SecurityViolation {
  type: 'csp' | 'xss' | 'csrf' | 'rate_limit'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  timestamp: number
  userAgent: string
  url: string
  details?: any
}

class SecurityService {
  private config: SecurityConfig
  private violations: SecurityViolation[] = []
  private rateLimitStore = new Map<string, { count: number; resetTime: number }>()

  constructor(config: Partial<SecurityConfig> = {}) {
    this.config = {
      csp: {
        enabled: true,
        directives: {
          'default-src': ["'self'"],
          'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
          'style-src': ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
          'font-src': ["'self'", 'https://fonts.gstatic.com'],
          'img-src': ["'self'", 'data:', 'https:'],
          'connect-src': ["'self'", 'ws:', 'wss:'],
          'frame-ancestors': ["'none'"],
          'base-uri': ["'self'"],
          'form-action': ["'self'"]
        }
      },
      xss: {
        enabled: true,
        allowedTags: [
          'div', 'span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
          'strong', 'em', 'u', 'br', 'hr', 'a', 'img', 'ul', 'ol', 'li',
          'table', 'thead', 'tbody', 'tr', 'td', 'th', 'blockquote'
        ],
        allowedAttributes: {
          'a': ['href', 'title', 'target'],
          'img': ['src', 'alt', 'width', 'height', 'style'],
          'div': ['class', 'style', 'id'],
          'span': ['class', 'style'],
          'p': ['class', 'style'],
          'h1': ['class', 'style'],
          'h2': ['class', 'style'],
          'h3': ['class', 'style'],
          'h4': ['class', 'style'],
          'h5': ['class', 'style'],
          'h6': ['class', 'style'],
          'table': ['class', 'style'],
          'td': ['class', 'style', 'colspan', 'rowspan'],
          'th': ['class', 'style', 'colspan', 'rowspan']
        }
      },
      csrf: {
        enabled: true,
        tokenName: 'X-CSRF-Token'
      },
      rateLimit: {
        enabled: true,
        maxRequests: 100,
        windowMs: 15 * 60 * 1000 // 15 minutes
      },
      ...config
    }

    this.initializeSecurity()
  }

  private initializeSecurity(): void {
    if (this.config.csp.enabled) {
      this.setupCSP()
    }

    if (this.config.xss.enabled) {
      this.setupXSSProtection()
    }

    if (this.config.csrf.enabled) {
      this.setupCSRFProtection()
    }

    this.setupSecurityHeaders()
    this.setupViolationReporting()
  }

  // Content Security Policy
  private setupCSP(): void {
    const cspString = Object.entries(this.config.csp.directives)
      .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
      .join('; ')

    // Set CSP header via meta tag (for client-side)
    const metaTag = document.createElement('meta')
    metaTag.httpEquiv = 'Content-Security-Policy'
    metaTag.content = cspString
    document.head.appendChild(metaTag)

    // Listen for CSP violations
    document.addEventListener('securitypolicyviolation', (event) => {
      this.reportViolation({
        type: 'csp',
        severity: 'high',
        message: `CSP violation: ${event.violatedDirective}`,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        details: {
          violatedDirective: event.violatedDirective,
          blockedURI: event.blockedURI,
          originalPolicy: event.originalPolicy
        }
      })
    })
  }

  // XSS Protection
  private setupXSSProtection(): void {
    // Configure DOMPurify
    DOMPurify.setConfig({
      ALLOWED_TAGS: this.config.xss.allowedTags,
      ALLOWED_ATTR: Object.values(this.config.xss.allowedAttributes).flat(),
      ALLOW_DATA_ATTR: false,
      ALLOW_UNKNOWN_PROTOCOLS: false,
      SANITIZE_DOM: true,
      KEEP_CONTENT: true
    })

    // Add hooks for violation reporting
    DOMPurify.addHook('uponSanitizeElement', (node, data) => {
      if (data.allowedTags && !data.allowedTags[data.tagName]) {
        this.reportViolation({
          type: 'xss',
          severity: 'medium',
          message: `Blocked potentially malicious tag: ${data.tagName}`,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          url: window.location.href,
          details: { tagName: data.tagName, outerHTML: node.outerHTML }
        })
      }
    })
  }

  // CSRF Protection
  private setupCSRFProtection(): void {
    // Generate CSRF token
    const token = this.generateCSRFToken()
    localStorage.setItem('csrf_token', token)

    // Add token to all forms
    const forms = document.querySelectorAll('form')
    forms.forEach(form => {
      const input = document.createElement('input')
      input.type = 'hidden'
      input.name = this.config.csrf.tokenName
      input.value = token
      form.appendChild(input)
    })
  }

  private generateCSRFToken(): string {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  // Security Headers
  private setupSecurityHeaders(): void {
    // These would typically be set by the server, but we can add some client-side protections
    
    // Prevent clickjacking
    if (window.self !== window.top) {
      document.body.style.display = 'none'
      this.reportViolation({
        type: 'csrf',
        severity: 'high',
        message: 'Potential clickjacking attempt detected',
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href
      })
    }

    // Disable right-click in production (optional)
    if (process.env.NODE_ENV === 'production') {
      document.addEventListener('contextmenu', (e) => {
        e.preventDefault()
      })
    }
  }

  // Violation Reporting
  private setupViolationReporting(): void {
    // Report violations to server
    window.addEventListener('unhandledrejection', (event) => {
      this.reportViolation({
        type: 'xss',
        severity: 'medium',
        message: `Unhandled promise rejection: ${event.reason}`,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        details: { reason: event.reason }
      })
    })
  }

  // Public API
  sanitizeHTML(html: string): string {
    if (!this.config.xss.enabled) {
      return html
    }

    try {
      return DOMPurify.sanitize(html, {
        RETURN_DOM_FRAGMENT: false,
        RETURN_DOM: false
      })
    } catch (error) {
      this.reportViolation({
        type: 'xss',
        severity: 'high',
        message: `HTML sanitization failed: ${error}`,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        details: { originalHTML: html, error: error.toString() }
      })
      return '' // Return empty string on sanitization failure
    }
  }

  validateInput(input: string, type: 'email' | 'url' | 'text' | 'html'): boolean {
    switch (type) {
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return emailRegex.test(input)
      
      case 'url':
        try {
          new URL(input)
          return true
        } catch {
          return false
        }
      
      case 'text':
        // Check for potential XSS patterns
        const xssPatterns = [
          /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
          /javascript:/gi,
          /on\w+\s*=/gi,
          /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi
        ]
        return !xssPatterns.some(pattern => pattern.test(input))
      
      case 'html':
        // Use DOMPurify to validate
        const sanitized = this.sanitizeHTML(input)
        return sanitized === input
      
      default:
        return true
    }
  }

  checkRateLimit(identifier: string): boolean {
    if (!this.config.rateLimit.enabled) {
      return true
    }

    const now = Date.now()
    const key = identifier
    const limit = this.rateLimitStore.get(key)

    if (!limit || now > limit.resetTime) {
      this.rateLimitStore.set(key, {
        count: 1,
        resetTime: now + this.config.rateLimit.windowMs
      })
      return true
    }

    if (limit.count >= this.config.rateLimit.maxRequests) {
      this.reportViolation({
        type: 'rate_limit',
        severity: 'medium',
        message: `Rate limit exceeded for ${identifier}`,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        details: { identifier, count: limit.count, maxRequests: this.config.rateLimit.maxRequests }
      })
      return false
    }

    limit.count++
    return true
  }

  getCSRFToken(): string | null {
    return localStorage.getItem('csrf_token')
  }

  reportViolation(violation: SecurityViolation): void {
    this.violations.push(violation)

    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.warn('Security violation:', violation)
    }

    // Send to server in production
    if (process.env.NODE_ENV === 'production') {
      fetch('/api/security/violations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          [this.config.csrf.tokenName]: this.getCSRFToken() || ''
        },
        body: JSON.stringify(violation)
      }).catch(error => {
        console.error('Failed to report security violation:', error)
      })
    }
  }

  getViolations(): SecurityViolation[] {
    return [...this.violations]
  }

  clearViolations(): void {
    this.violations = []
  }

  // Template-specific security
  validateTemplateContent(content: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    // Check for malicious scripts
    const contentString = JSON.stringify(content)
    if (contentString.includes('<script>') || contentString.includes('javascript:')) {
      errors.push('Template contains potentially malicious scripts')
    }

    // Check for external resource loading
    if (contentString.includes('http://') && !contentString.includes('https://')) {
      errors.push('Template contains insecure HTTP resources')
    }

    // Validate component structure
    if (content.components) {
      content.components.forEach((component: any, index: number) => {
        if (!component.id || !component.type) {
          errors.push(`Component ${index} is missing required fields`)
        }

        if (component.content && typeof component.content === 'string') {
          if (!this.validateInput(component.content, 'html')) {
            errors.push(`Component ${index} contains invalid HTML`)
          }
        }
      })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // Secure storage
  secureStore(key: string, value: any): void {
    try {
      const encrypted = btoa(JSON.stringify(value))
      localStorage.setItem(`secure_${key}`, encrypted)
    } catch (error) {
      console.error('Failed to securely store data:', error)
    }
  }

  secureRetrieve(key: string): any {
    try {
      const encrypted = localStorage.getItem(`secure_${key}`)
      if (!encrypted) return null
      
      return JSON.parse(atob(encrypted))
    } catch (error) {
      console.error('Failed to securely retrieve data:', error)
      return null
    }
  }

  secureRemove(key: string): void {
    localStorage.removeItem(`secure_${key}`)
  }
}

// Create singleton instance
export const securityService = new SecurityService()

// Export for custom configurations
export { SecurityService }
export type { SecurityConfig, SecurityViolation }
