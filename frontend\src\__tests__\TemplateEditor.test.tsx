import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import TemplateEditor from '../pages/TemplateEditor'
import { AuthProvider } from '../contexts/AuthContext'
import { NotificationProvider } from '../components/NotificationSystem'
import { 
  mockTemplate, 
  mockComponent, 
  setupTestEnvironment,
  createMockEvent,
  createMockKeyboardEvent
} from '../utils/testHelpers'

// Mock the API services
jest.mock('../services/api', () => ({
  apiService: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn()
  }
}))

jest.mock('../services/templateEditorApi', () => ({
  templateEditorApi: {
    getTemplate: jest.fn(),
    createTemplate: jest.fn(),
    updateTemplate: jest.fn(),
    deleteTemplate: jest.fn(),
    getTemplateVersions: jest.fn(),
    generateHTML: jest.fn()
  }
}))

// Mock react-router-dom
const mockNavigate = jest.fn()
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useParams: () => ({ id: '1' })
}))

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <AuthProvider>
      <NotificationProvider>
        <DndProvider backend={HTML5Backend}>
          {children}
        </DndProvider>
      </NotificationProvider>
    </AuthProvider>
  </BrowserRouter>
)

describe('TemplateEditor', () => {
  let cleanup: () => void

  beforeEach(() => {
    cleanup = setupTestEnvironment()
    jest.clearAllMocks()
  })

  afterEach(() => {
    cleanup()
  })

  describe('Component Rendering', () => {
    it('renders template editor interface', async () => {
      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      expect(screen.getByText('Back to Templates')).toBeInTheDocument()
      expect(screen.getByText('Components')).toBeInTheDocument()
    })

    it('shows loading state initially', () => {
      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      expect(screen.getByText('Loading...')).toBeInTheDocument()
    })
  })

  describe('Template Loading', () => {
    it('loads existing template when editing', async () => {
      const { templateEditorApi } = require('../services/templateEditorApi')
      templateEditorApi.getTemplate.mockResolvedValue(mockTemplate)
      templateEditorApi.getTemplateVersions.mockResolvedValue([])

      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(templateEditorApi.getTemplate).toHaveBeenCalledWith('1')
      })
    })

    it('handles template loading errors', async () => {
      const { templateEditorApi } = require('../services/templateEditorApi')
      templateEditorApi.getTemplate.mockRejectedValue(new Error('Template not found'))

      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/templates')
      })
    })
  })

  describe('Component Management', () => {
    it('adds component to canvas', async () => {
      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      const textComponent = screen.getByText('Text')
      fireEvent.click(textComponent)

      // Component should be added to the canvas
      await waitFor(() => {
        expect(screen.getByText('Your text here...')).toBeInTheDocument()
      })
    })

    it('selects component when clicked', async () => {
      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      // Add a component first
      const textComponent = screen.getByText('Text')
      fireEvent.click(textComponent)

      await waitFor(() => {
        const addedComponent = screen.getByText('Your text here...')
        fireEvent.click(addedComponent)
        
        // Component should be selected (visual indication would be tested with CSS classes)
        expect(addedComponent).toBeInTheDocument()
      })
    })

    it('deletes selected component with keyboard shortcut', async () => {
      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      // Add and select a component
      const textComponent = screen.getByText('Text')
      fireEvent.click(textComponent)

      await waitFor(() => {
        const addedComponent = screen.getByText('Your text here...')
        fireEvent.click(addedComponent)
      })

      // Press delete key
      fireEvent.keyDown(document, { key: 'Delete' })

      await waitFor(() => {
        expect(screen.queryByText('Your text here...')).not.toBeInTheDocument()
      })
    })
  })

  describe('Keyboard Shortcuts', () => {
    it('saves template with Ctrl+S', async () => {
      const { templateEditorApi } = require('../services/templateEditorApi')
      templateEditorApi.updateTemplate.mockResolvedValue({})

      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      // Set template name
      const nameInput = screen.getByDisplayValue('Untitled Template')
      fireEvent.change(nameInput, { target: { value: 'Test Template' } })

      // Press Ctrl+S
      fireEvent.keyDown(document, { key: 's', ctrlKey: true })

      await waitFor(() => {
        expect(templateEditorApi.updateTemplate).toHaveBeenCalled()
      })
    })

    it('undoes action with Ctrl+Z', async () => {
      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      // Add a component
      const textComponent = screen.getByText('Text')
      fireEvent.click(textComponent)

      await waitFor(() => {
        expect(screen.getByText('Your text here...')).toBeInTheDocument()
      })

      // Press Ctrl+Z to undo
      fireEvent.keyDown(document, { key: 'z', ctrlKey: true })

      await waitFor(() => {
        expect(screen.queryByText('Your text here...')).not.toBeInTheDocument()
      })
    })

    it('redoes action with Ctrl+Y', async () => {
      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      // Add a component
      const textComponent = screen.getByText('Text')
      fireEvent.click(textComponent)

      await waitFor(() => {
        expect(screen.getByText('Your text here...')).toBeInTheDocument()
      })

      // Undo
      fireEvent.keyDown(document, { key: 'z', ctrlKey: true })

      await waitFor(() => {
        expect(screen.queryByText('Your text here...')).not.toBeInTheDocument()
      })

      // Redo
      fireEvent.keyDown(document, { key: 'y', ctrlKey: true })

      await waitFor(() => {
        expect(screen.getByText('Your text here...')).toBeInTheDocument()
      })
    })
  })

  describe('Template Saving', () => {
    it('creates new template when not editing', async () => {
      const { templateEditorApi } = require('../services/templateEditorApi')
      templateEditorApi.createTemplate.mockResolvedValue({ id: '2' })

      // Mock useParams to return no ID (new template)
      jest.doMock('react-router-dom', () => ({
        ...jest.requireActual('react-router-dom'),
        useNavigate: () => mockNavigate,
        useParams: () => ({})
      }))

      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      // Set template name
      const nameInput = screen.getByDisplayValue('Untitled Template')
      fireEvent.change(nameInput, { target: { value: 'New Template' } })

      // Click save button
      const saveButton = screen.getByText('Save Template')
      fireEvent.click(saveButton)

      await waitFor(() => {
        expect(templateEditorApi.createTemplate).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'New Template'
          })
        )
      })
    })

    it('updates existing template when editing', async () => {
      const { templateEditorApi } = require('../services/templateEditorApi')
      templateEditorApi.getTemplate.mockResolvedValue(mockTemplate)
      templateEditorApi.getTemplateVersions.mockResolvedValue([])
      templateEditorApi.updateTemplate.mockResolvedValue({})

      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(templateEditorApi.getTemplate).toHaveBeenCalled()
      })

      // Click save button
      const saveButton = screen.getByText('Save Template')
      fireEvent.click(saveButton)

      await waitFor(() => {
        expect(templateEditorApi.updateTemplate).toHaveBeenCalledWith(
          '1',
          expect.any(Object)
        )
      })
    })

    it('shows validation error when saving without name', async () => {
      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      // Clear template name
      const nameInput = screen.getByDisplayValue('Untitled Template')
      fireEvent.change(nameInput, { target: { value: '' } })

      // Click save button
      const saveButton = screen.getByText('Save Template')
      fireEvent.click(saveButton)

      await waitFor(() => {
        expect(screen.getByText('Please enter a template name before saving.')).toBeInTheDocument()
      })
    })
  })

  describe('Error Handling', () => {
    it('handles save errors gracefully', async () => {
      const { templateEditorApi } = require('../services/templateEditorApi')
      templateEditorApi.updateTemplate.mockRejectedValue(new Error('Save failed'))

      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      // Set template name
      const nameInput = screen.getByDisplayValue('Untitled Template')
      fireEvent.change(nameInput, { target: { value: 'Test Template' } })

      // Click save button
      const saveButton = screen.getByText('Save Template')
      fireEvent.click(saveButton)

      await waitFor(() => {
        expect(screen.getByText('Failed to save template')).toBeInTheDocument()
      })
    })

    it('shows error boundary when component crashes', () => {
      // Mock console.error to avoid noise in test output
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()

      // Create a component that throws an error
      const ThrowError = () => {
        throw new Error('Test error')
      }

      render(
        <TestWrapper>
          <ThrowError />
        </TestWrapper>
      )

      expect(screen.getByText('Something went wrong')).toBeInTheDocument()
      
      consoleSpy.mockRestore()
    })
  })

  describe('Performance', () => {
    it('renders large number of components efficiently', async () => {
      const startTime = performance.now()

      render(
        <TestWrapper>
          <TemplateEditor />
        </TestWrapper>
      )

      // Add multiple components
      const textComponent = screen.getByText('Text')
      for (let i = 0; i < 50; i++) {
        fireEvent.click(textComponent)
      }

      const endTime = performance.now()
      const renderTime = endTime - startTime

      // Should render within reasonable time (adjust threshold as needed)
      expect(renderTime).toBeLessThan(5000) // 5 seconds
    })
  })
})
