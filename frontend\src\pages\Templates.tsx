import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Plus, FileText, Edit, Trash2, <PERSON><PERSON>, AlertCircle, Loader2, Search, SortAsc, SortDesc } from 'lucide-react'
import { templateEditor<PERSON>pi, TemplateData } from '../services/templateEditorApi'
import { useNotifications } from '../contexts/NotificationContext'

const Templates: React.FC = () => {
  const { success, error: showError, confirm } = useNotifications()
  const [templates, setTemplates] = useState<TemplateData[]>([])
  const [filteredTemplates, setFilteredTemplates] = useState<TemplateData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [deletingId, setDeletingId] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<'name' | 'updated_at' | 'created_at'>('updated_at')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // Load templates on component mount
  useEffect(() => {
    loadTemplates()
  }, [])

  // Filter and sort templates
  useEffect(() => {
    let filtered = templates.filter(template =>
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (template.description && template.description.toLowerCase().includes(searchTerm.toLowerCase()))
    )

    // Sort templates
    filtered.sort((a, b) => {
      let aValue: any, bValue: any

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
        case 'created_at':
          aValue = new Date(a.created_at || 0)
          bValue = new Date(b.created_at || 0)
          break
        case 'updated_at':
        default:
          aValue = new Date(a.updated_at || 0)
          bValue = new Date(b.updated_at || 0)
          break
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    setFilteredTemplates(filtered)
  }, [templates, searchTerm, sortBy, sortOrder])

  const loadTemplates = async () => {
    try {
      setLoading(true)
      setError(null)
      const templatesData = await templateEditorApi.getTemplates()
      setTemplates(templatesData)
    } catch (err) {
      console.error('Failed to load templates:', err)
      setError('Failed to load templates. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteTemplate = async (templateId: string) => {
    const confirmed = await confirm(
      'Delete Template',
      'Are you sure you want to delete this template? This action cannot be undone.'
    )

    if (!confirmed) return

    try {
      setDeletingId(templateId)
      await templateEditorApi.deleteTemplate(templateId)
      setTemplates(templates.filter(t => t.id?.toString() !== templateId))
      success('Template deleted successfully')
    } catch (err) {
      console.error('Failed to delete template:', err)
      showError('Failed to delete template', 'Please try again.')
    } finally {
      setDeletingId(null)
    }
  }

  const handleCopyTemplate = async (template: TemplateData) => {
    try {
      const newTemplate = await templateEditorApi.createTemplate({
        name: `${template.name} (Copy)`,
        description: template.description,
        html_content: template.html_content,
        components: template.components,
        settings: template.settings
      })
      setTemplates([newTemplate, ...templates])
      success('Template copied successfully', `Created "${newTemplate.name}"`)
    } catch (err) {
      console.error('Failed to copy template:', err)
      showError('Failed to copy template', 'Please try again.')
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-600">Loading templates...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Templates</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={loadTemplates}
              className="btn btn-primary"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Email Templates</h1>
          <p className="text-gray-600">Create and manage your email templates</p>
        </div>
        <Link
          to="/templates/new"
          className="btn btn-primary flex items-center"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Template
        </Link>
      </div>

      {/* Search and Filter Bar */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="flex gap-2">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'name' | 'updated_at' | 'created_at')}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="updated_at">Sort by Updated</option>
            <option value="created_at">Sort by Created</option>
            <option value="name">Sort by Name</option>
          </select>

          <button
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
          >
            {sortOrder === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <div key={template.id} className="card overflow-hidden">
            <div className="h-48 bg-gray-100 flex items-center justify-center">
              {template.thumbnail ? (
                <img
                  src={template.thumbnail}
                  alt={template.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <FileText className="w-16 h-16 text-gray-400" />
              )}
            </div>
            <div className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {template.name}
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {template.description || 'No description'}
              </p>
              <div className="text-xs text-gray-500 mb-4">
                Updated {template.updated_at ? new Date(template.updated_at).toLocaleDateString() : 'Unknown'}
              </div>
              <div className="flex items-center space-x-2">
                <Link
                  to={`/templates/edit/${template.id}`}
                  className="btn btn-outline flex-1 flex items-center justify-center"
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Edit
                </Link>
                <button
                  onClick={() => handleCopyTemplate(template)}
                  className="btn btn-outline p-2"
                  title="Copy template"
                >
                  <Copy className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDeleteTemplate(template.id?.toString() || '')}
                  disabled={deletingId === template.id?.toString()}
                  className="btn btn-outline p-2 text-red-600 hover:text-red-700 disabled:opacity-50"
                  title="Delete template"
                >
                  {deletingId === template.id?.toString() ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {templates.length === 0 && (
        <div className="text-center py-12">
          <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No templates yet
          </h3>
          <p className="text-gray-600 mb-6">
            Get started by creating your first email template
          </p>
          <Link
            to="/templates/new"
            className="btn btn-primary"
          >
            Create Your First Template
          </Link>
        </div>
      )}

      {templates.length > 0 && filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No templates found
          </h3>
          <p className="text-gray-600 mb-6">
            Try adjusting your search terms or filters
          </p>
          <button
            onClick={() => setSearchTerm('')}
            className="btn btn-outline"
          >
            Clear Search
          </button>
        </div>
      )}
    </div>
  )
}

export default Templates
