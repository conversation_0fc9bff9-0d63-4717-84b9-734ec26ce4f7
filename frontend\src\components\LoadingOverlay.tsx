import React from 'react'
import { Loader2 } from 'lucide-react'

interface LoadingOverlayProps {
  isLoading: boolean
  message?: string
  size?: 'sm' | 'md' | 'lg'
  overlay?: boolean
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  message = 'Loading...',
  size = 'md',
  overlay = true
}) => {
  if (!isLoading) return null

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }

  if (overlay) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 flex flex-col items-center space-y-4 shadow-xl">
          <Loader2 className={`${sizeClasses[size]} animate-spin text-primary-600`} />
          <p className={`${textSizeClasses[size]} text-gray-700 font-medium`}>
            {message}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex items-center justify-center space-x-2">
      <Loader2 className={`${sizeClasses[size]} animate-spin text-primary-600`} />
      <span className={`${textSizeClasses[size]} text-gray-700`}>
        {message}
      </span>
    </div>
  )
}

export default LoadingOverlay

// Inline loading component for buttons
export const ButtonLoader: React.FC<{ size?: 'sm' | 'md' }> = ({ size = 'sm' }) => {
  const sizeClass = size === 'sm' ? 'w-4 h-4' : 'w-5 h-5'

  return (
    <Loader2 className={`${sizeClass} animate-spin`} />
  )
}

// Page loading component
export const PageLoader: React.FC<{ message?: string }> = ({
  message = 'Loading page...'
}) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <Loader2 className="w-12 h-12 animate-spin text-primary-600 mx-auto mb-4" />
        <p className="text-lg text-gray-700 font-medium">{message}</p>
      </div>
    </div>
  )
}
