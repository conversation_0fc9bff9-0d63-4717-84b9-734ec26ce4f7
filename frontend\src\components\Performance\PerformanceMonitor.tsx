import React, { useState, useEffect, useRef } from 'react'
import { <PERSON><PERSON>hart, Line, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts'
import { Activity, Cpu, HardDrive, Wifi, Alert<PERSON>riangle, CheckCircle, Clock, Zap } from 'lucide-react'

interface PerformanceMetrics {
  timestamp: number
  memory: {
    used: number
    total: number
    limit: number
  }
  timing: {
    renderTime: number
    apiResponseTime: number
    domContentLoaded: number
    firstPaint: number
  }
  network: {
    effectiveType: string
    downlink: number
    rtt: number
  }
  errors: {
    count: number
    types: Record<string, number>
  }
  userInteractions: {
    clicks: number
    scrolls: number
    keystrokes: number
  }
}

interface PerformanceAlert {
  id: string
  type: 'warning' | 'error' | 'info'
  message: string
  timestamp: number
  metric: string
  value: number
  threshold: number
}

const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([])
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([])
  const [isMonitoring, setIsMonitoring] = useState(false)
  const [selectedMetric, setSelectedMetric] = useState<'memory' | 'timing' | 'network'>('memory')
  const intervalRef = useRef<NodeJS.Timeout>()
  const metricsRef = useRef<PerformanceMetrics[]>([])

  // Performance thresholds
  const thresholds = {
    memory: { warning: 50, error: 80 }, // MB
    renderTime: { warning: 100, error: 200 }, // ms
    apiResponseTime: { warning: 1000, error: 3000 }, // ms
    networkRTT: { warning: 200, error: 500 } // ms
  }

  useEffect(() => {
    if (isMonitoring) {
      startMonitoring()
    } else {
      stopMonitoring()
    }

    return () => stopMonitoring()
  }, [isMonitoring])

  const startMonitoring = () => {
    intervalRef.current = setInterval(() => {
      collectMetrics()
    }, 1000) // Collect metrics every second
  }

  const stopMonitoring = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
  }

  const collectMetrics = () => {
    const now = Date.now()
    
    // Memory metrics
    const memory = getMemoryMetrics()
    
    // Timing metrics
    const timing = getTimingMetrics()
    
    // Network metrics
    const network = getNetworkMetrics()
    
    // Error metrics
    const errors = getErrorMetrics()
    
    // User interaction metrics
    const userInteractions = getUserInteractionMetrics()

    const newMetrics: PerformanceMetrics = {
      timestamp: now,
      memory,
      timing,
      network,
      errors,
      userInteractions
    }

    // Update metrics array (keep last 60 data points)
    metricsRef.current = [...metricsRef.current.slice(-59), newMetrics]
    setMetrics([...metricsRef.current])

    // Check for alerts
    checkAlerts(newMetrics)
  }

  const getMemoryMetrics = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) // MB
      }
    }
    return { used: 0, total: 0, limit: 0 }
  }

  const getTimingMetrics = () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    
    return {
      renderTime: Math.round(performance.now()), // Current render time
      apiResponseTime: getAverageApiResponseTime(),
      domContentLoaded: navigation ? Math.round(navigation.domContentLoadedEventEnd - navigation.fetchStart) : 0,
      firstPaint: getFirstPaintTime()
    }
  }

  const getNetworkMetrics = () => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      return {
        effectiveType: connection.effectiveType || 'unknown',
        downlink: connection.downlink || 0,
        rtt: connection.rtt || 0
      }
    }
    return { effectiveType: 'unknown', downlink: 0, rtt: 0 }
  }

  const getErrorMetrics = () => {
    // This would typically come from a global error handler
    return {
      count: 0,
      types: {}
    }
  }

  const getUserInteractionMetrics = () => {
    // This would typically be tracked by event listeners
    return {
      clicks: 0,
      scrolls: 0,
      keystrokes: 0
    }
  }

  const getAverageApiResponseTime = () => {
    const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
    const apiCalls = resourceEntries.filter(entry => entry.name.includes('/api/'))
    
    if (apiCalls.length === 0) return 0
    
    const totalTime = apiCalls.reduce((sum, entry) => sum + entry.duration, 0)
    return Math.round(totalTime / apiCalls.length)
  }

  const getFirstPaintTime = () => {
    const paintEntries = performance.getEntriesByType('paint')
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint')
    return firstPaint ? Math.round(firstPaint.startTime) : 0
  }

  const checkAlerts = (metrics: PerformanceMetrics) => {
    const newAlerts: PerformanceAlert[] = []

    // Memory alerts
    const memoryUsagePercent = (metrics.memory.used / metrics.memory.limit) * 100
    if (memoryUsagePercent > thresholds.memory.error) {
      newAlerts.push({
        id: `memory-${Date.now()}`,
        type: 'error',
        message: `High memory usage: ${memoryUsagePercent.toFixed(1)}%`,
        timestamp: metrics.timestamp,
        metric: 'memory',
        value: memoryUsagePercent,
        threshold: thresholds.memory.error
      })
    } else if (memoryUsagePercent > thresholds.memory.warning) {
      newAlerts.push({
        id: `memory-${Date.now()}`,
        type: 'warning',
        message: `Elevated memory usage: ${memoryUsagePercent.toFixed(1)}%`,
        timestamp: metrics.timestamp,
        metric: 'memory',
        value: memoryUsagePercent,
        threshold: thresholds.memory.warning
      })
    }

    // Render time alerts
    if (metrics.timing.renderTime > thresholds.renderTime.error) {
      newAlerts.push({
        id: `render-${Date.now()}`,
        type: 'error',
        message: `Slow render time: ${metrics.timing.renderTime}ms`,
        timestamp: metrics.timestamp,
        metric: 'renderTime',
        value: metrics.timing.renderTime,
        threshold: thresholds.renderTime.error
      })
    }

    // API response time alerts
    if (metrics.timing.apiResponseTime > thresholds.apiResponseTime.error) {
      newAlerts.push({
        id: `api-${Date.now()}`,
        type: 'error',
        message: `Slow API response: ${metrics.timing.apiResponseTime}ms`,
        timestamp: metrics.timestamp,
        metric: 'apiResponseTime',
        value: metrics.timing.apiResponseTime,
        threshold: thresholds.apiResponseTime.error
      })
    }

    if (newAlerts.length > 0) {
      setAlerts(prev => [...prev.slice(-9), ...newAlerts]) // Keep last 10 alerts
    }
  }

  const getChartData = () => {
    return metrics.map(metric => ({
      time: new Date(metric.timestamp).toLocaleTimeString(),
      timestamp: metric.timestamp,
      ...getMetricValues(metric)
    }))
  }

  const getMetricValues = (metric: PerformanceMetrics) => {
    switch (selectedMetric) {
      case 'memory':
        return {
          used: metric.memory.used,
          total: metric.memory.total,
          usage: (metric.memory.used / metric.memory.limit) * 100
        }
      case 'timing':
        return {
          renderTime: metric.timing.renderTime,
          apiResponseTime: metric.timing.apiResponseTime,
          domContentLoaded: metric.timing.domContentLoaded
        }
      case 'network':
        return {
          rtt: metric.network.rtt,
          downlink: metric.network.downlink * 1000 // Convert to kbps
        }
      default:
        return {}
    }
  }

  const getCurrentMetrics = () => {
    return metrics.length > 0 ? metrics[metrics.length - 1] : null
  }

  const currentMetrics = getCurrentMetrics()

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Performance Monitor</h2>
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setIsMonitoring(!isMonitoring)}
            className={`px-4 py-2 rounded-md font-medium ${
              isMonitoring
                ? 'bg-red-600 text-white hover:bg-red-700'
                : 'bg-green-600 text-white hover:bg-green-700'
            }`}
          >
            {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
          </button>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isMonitoring ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
            <span className="text-sm text-gray-600">
              {isMonitoring ? 'Monitoring' : 'Stopped'}
            </span>
          </div>
        </div>
      </div>

      {/* Current Metrics Cards */}
      {currentMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Memory Usage</p>
                <p className="text-2xl font-bold text-gray-900">
                  {((currentMetrics.memory.used / currentMetrics.memory.limit) * 100).toFixed(1)}%
                </p>
                <p className="text-sm text-gray-500">
                  {currentMetrics.memory.used}MB / {currentMetrics.memory.limit}MB
                </p>
              </div>
              <HardDrive className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Render Time</p>
                <p className="text-2xl font-bold text-gray-900">
                  {currentMetrics.timing.renderTime}ms
                </p>
                <p className="text-sm text-gray-500">Current frame</p>
              </div>
              <Cpu className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Network RTT</p>
                <p className="text-2xl font-bold text-gray-900">
                  {currentMetrics.network.rtt}ms
                </p>
                <p className="text-sm text-gray-500">
                  {currentMetrics.network.effectiveType}
                </p>
              </div>
              <Wifi className="w-8 h-8 text-purple-500" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">API Response</p>
                <p className="text-2xl font-bold text-gray-900">
                  {currentMetrics.timing.apiResponseTime}ms
                </p>
                <p className="text-sm text-gray-500">Average</p>
              </div>
              <Zap className="w-8 h-8 text-yellow-500" />
            </div>
          </div>
        </div>
      )}

      {/* Charts */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Performance Trends</h3>
          <select
            value={selectedMetric}
            onChange={(e) => setSelectedMetric(e.target.value as any)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="memory">Memory Usage</option>
            <option value="timing">Timing Metrics</option>
            <option value="network">Network Metrics</option>
          </select>
        </div>

        {metrics.length > 0 ? (
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={getChartData()}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="time" />
              <YAxis />
              <Tooltip />
              {selectedMetric === 'memory' && (
                <>
                  <Area
                    type="monotone"
                    dataKey="usage"
                    stroke="#3B82F6"
                    fill="#3B82F6"
                    fillOpacity={0.1}
                    name="Memory Usage %"
                  />
                </>
              )}
              {selectedMetric === 'timing' && (
                <>
                  <Area
                    type="monotone"
                    dataKey="renderTime"
                    stroke="#10B981"
                    fill="#10B981"
                    fillOpacity={0.1}
                    name="Render Time (ms)"
                  />
                  <Area
                    type="monotone"
                    dataKey="apiResponseTime"
                    stroke="#F59E0B"
                    fill="#F59E0B"
                    fillOpacity={0.1}
                    name="API Response (ms)"
                  />
                </>
              )}
              {selectedMetric === 'network' && (
                <>
                  <Area
                    type="monotone"
                    dataKey="rtt"
                    stroke="#8B5CF6"
                    fill="#8B5CF6"
                    fillOpacity={0.1}
                    name="RTT (ms)"
                  />
                </>
              )}
            </AreaChart>
          </ResponsiveContainer>
        ) : (
          <div className="h-64 flex items-center justify-center text-gray-500">
            {isMonitoring ? 'Collecting performance data...' : 'Start monitoring to see performance data'}
          </div>
        )}
      </div>

      {/* Alerts */}
      {alerts.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Alerts</h3>
          <div className="space-y-3">
            {alerts.slice(-5).map((alert) => (
              <div
                key={alert.id}
                className={`p-3 rounded-md border-l-4 ${
                  alert.type === 'error'
                    ? 'bg-red-50 border-red-400'
                    : alert.type === 'warning'
                    ? 'bg-yellow-50 border-yellow-400'
                    : 'bg-blue-50 border-blue-400'
                }`}
              >
                <div className="flex items-center space-x-2">
                  {alert.type === 'error' ? (
                    <AlertTriangle className="w-5 h-5 text-red-500" />
                  ) : alert.type === 'warning' ? (
                    <AlertTriangle className="w-5 h-5 text-yellow-500" />
                  ) : (
                    <CheckCircle className="w-5 h-5 text-blue-500" />
                  )}
                  <span className="font-medium text-gray-900">{alert.message}</span>
                  <span className="text-sm text-gray-500">
                    {new Date(alert.timestamp).toLocaleTimeString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default PerformanceMonitor
