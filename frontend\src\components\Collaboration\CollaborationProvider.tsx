import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react'
import { collaborationService, CollaborationUser, generateUserColor } from '../../services/collaboration'
import { useAuth } from '../../contexts/AuthContext'
import { useNotifications } from '../NotificationSystem'

interface CollaborationContextType {
  isConnected: boolean
  connectedUsers: CollaborationUser[]
  currentUser: CollaborationUser | null
  connect: (templateId: string) => Promise<void>
  disconnect: () => Promise<void>
  updateCursor: (x: number, y: number) => void
  updateSelection: (componentIds: string[]) => void
  sendComponentAdd: (component: any) => void
  sendComponentUpdate: (componentId: string, updates: any) => void
  sendComponentDelete: (componentId: string) => void
}

const CollaborationContext = createContext<CollaborationContextType | undefined>(undefined)

export const useCollaboration = () => {
  const context = useContext(CollaborationContext)
  if (!context) {
    throw new Error('useCollaboration must be used within a CollaborationProvider')
  }
  return context
}

interface CollaborationProviderProps {
  children: ReactNode
  onRemoteComponentAdd?: (data: any) => void
  onRemoteComponentUpdate?: (data: any) => void
  onRemoteComponentDelete?: (data: any) => void
  onConflict?: (data: any) => void
}

export const CollaborationProvider: React.FC<CollaborationProviderProps> = ({
  children,
  onRemoteComponentAdd,
  onRemoteComponentUpdate,
  onRemoteComponentDelete,
  onConflict
}) => {
  const { user } = useAuth()
  const { info, warning, error } = useNotifications()
  
  const [isConnected, setIsConnected] = useState(false)
  const [connectedUsers, setConnectedUsers] = useState<CollaborationUser[]>([])
  const [currentUser, setCurrentUser] = useState<CollaborationUser | null>(null)

  // Setup event listeners
  useEffect(() => {
    const handleUserJoined = (user: CollaborationUser) => {
      setConnectedUsers(prev => [...prev.filter(u => u.id !== user.id), user])
      info(`${user.name} joined the collaboration`, { duration: 3000 })
    }

    const handleUserLeft = (userId: string) => {
      setConnectedUsers(prev => {
        const user = prev.find(u => u.id === userId)
        if (user) {
          info(`${user.name} left the collaboration`, { duration: 3000 })
        }
        return prev.filter(u => u.id !== userId)
      })
    }

    const handleUsersUpdated = (users: CollaborationUser[]) => {
      setConnectedUsers(users)
    }

    const handleDisconnected = () => {
      setIsConnected(false)
      warning('Disconnected from collaboration server')
    }

    const handleReconnected = () => {
      setIsConnected(true)
      info('Reconnected to collaboration server')
    }

    const handleConflictManual = (conflictData: any) => {
      warning('Conflict detected - manual resolution required')
      onConflict?.(conflictData)
    }

    // Register event handlers
    collaborationService.on('user_joined', handleUserJoined)
    collaborationService.on('user_left', handleUserLeft)
    collaborationService.on('users_updated', handleUsersUpdated)
    collaborationService.on('disconnected', handleDisconnected)
    collaborationService.on('reconnected', handleReconnected)
    collaborationService.on('remote_component_add', onRemoteComponentAdd || (() => {}))
    collaborationService.on('remote_component_update', onRemoteComponentUpdate || (() => {}))
    collaborationService.on('remote_component_delete', onRemoteComponentDelete || (() => {}))
    collaborationService.on('conflict_manual', handleConflictManual)

    return () => {
      // Cleanup event handlers
      collaborationService.off('user_joined', handleUserJoined)
      collaborationService.off('user_left', handleUserLeft)
      collaborationService.off('users_updated', handleUsersUpdated)
      collaborationService.off('disconnected', handleDisconnected)
      collaborationService.off('reconnected', handleReconnected)
      collaborationService.off('remote_component_add', onRemoteComponentAdd || (() => {}))
      collaborationService.off('remote_component_update', onRemoteComponentUpdate || (() => {}))
      collaborationService.off('remote_component_delete', onRemoteComponentDelete || (() => {}))
      collaborationService.off('conflict_manual', handleConflictManual)
    }
  }, [info, warning, onRemoteComponentAdd, onRemoteComponentUpdate, onRemoteComponentDelete, onConflict])

  const connect = useCallback(async (templateId: string) => {
    if (!user) {
      error('User must be authenticated to collaborate')
      return
    }

    try {
      const collaborationUser: Omit<CollaborationUser, 'lastSeen'> = {
        id: user.id,
        name: user.name,
        email: user.email,
        color: generateUserColor(user.id)
      }

      await collaborationService.connect(templateId, collaborationUser)
      setCurrentUser({ ...collaborationUser, lastSeen: Date.now() })
      setIsConnected(true)
      info('Connected to collaboration server')
    } catch (err) {
      error('Failed to connect to collaboration server')
      console.error('Collaboration connection error:', err)
    }
  }, [user, info, error])

  const disconnect = useCallback(async () => {
    try {
      await collaborationService.disconnect()
      setIsConnected(false)
      setConnectedUsers([])
      setCurrentUser(null)
    } catch (err) {
      console.error('Collaboration disconnect error:', err)
    }
  }, [])

  const updateCursor = useCallback((x: number, y: number) => {
    collaborationService.updateCursor(x, y)
  }, [])

  const updateSelection = useCallback((componentIds: string[]) => {
    collaborationService.updateSelection(componentIds)
  }, [])

  const sendComponentAdd = useCallback((component: any) => {
    collaborationService.addComponent(component)
  }, [])

  const sendComponentUpdate = useCallback((componentId: string, updates: any) => {
    collaborationService.updateComponent(componentId, updates)
  }, [])

  const sendComponentDelete = useCallback((componentId: string) => {
    collaborationService.deleteComponent(componentId)
  }, [])

  const value: CollaborationContextType = {
    isConnected,
    connectedUsers,
    currentUser,
    connect,
    disconnect,
    updateCursor,
    updateSelection,
    sendComponentAdd,
    sendComponentUpdate,
    sendComponentDelete
  }

  return (
    <CollaborationContext.Provider value={value}>
      {children}
    </CollaborationContext.Provider>
  )
}

// Collaboration status indicator component
export const CollaborationStatus: React.FC = () => {
  const { isConnected, connectedUsers } = useCollaboration()

  if (!isConnected) {
    return (
      <div className="flex items-center space-x-2 text-gray-500">
        <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
        <span className="text-sm">Offline</span>
      </div>
    )
  }

  return (
    <div className="flex items-center space-x-2">
      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
      <span className="text-sm text-gray-700">
        {connectedUsers.length} collaborator{connectedUsers.length !== 1 ? 's' : ''}
      </span>
    </div>
  )
}

// Connected users list component
export const ConnectedUsersList: React.FC = () => {
  const { connectedUsers, currentUser } = useCollaboration()

  if (connectedUsers.length === 0) {
    return null
  }

  return (
    <div className="flex items-center space-x-2">
      <span className="text-sm text-gray-600">Collaborators:</span>
      <div className="flex -space-x-2">
        {connectedUsers.slice(0, 5).map(user => (
          <div
            key={user.id}
            className="w-8 h-8 rounded-full border-2 border-white flex items-center justify-center text-white text-xs font-medium"
            style={{ backgroundColor: user.color }}
            title={user.name}
          >
            {user.name.charAt(0).toUpperCase()}
          </div>
        ))}
        {connectedUsers.length > 5 && (
          <div className="w-8 h-8 rounded-full border-2 border-white bg-gray-500 flex items-center justify-center text-white text-xs font-medium">
            +{connectedUsers.length - 5}
          </div>
        )}
      </div>
    </div>
  )
}

// User cursor component
export const UserCursor: React.FC<{
  user: CollaborationUser
  x: number
  y: number
}> = ({ user, x, y }) => {
  return (
    <div
      className="absolute pointer-events-none z-50 transition-all duration-100"
      style={{
        left: x,
        top: y,
        transform: 'translate(-2px, -2px)'
      }}
    >
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
        <path
          d="M0 0L20 7L8 10L5 20L0 0Z"
          fill={user.color}
          stroke="white"
          strokeWidth="1"
        />
      </svg>
      <div
        className="absolute top-5 left-2 px-2 py-1 rounded text-white text-xs whitespace-nowrap"
        style={{ backgroundColor: user.color }}
      >
        {user.name}
      </div>
    </div>
  )
}

export default CollaborationProvider
