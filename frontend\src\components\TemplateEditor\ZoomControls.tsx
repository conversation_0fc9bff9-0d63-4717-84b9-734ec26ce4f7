import React from 'react'
import { ZoomIn, ZoomOut, RotateCcw } from 'lucide-react'

interface ZoomControlsProps {
  zoom: number
  onZoomChange?: (zoom: number) => void
}

export const ZoomControls: React.FC<ZoomControlsProps> = ({ zoom, onZoomChange }) => {
  const zoomLevels = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 2]
  
  const handleZoomIn = () => {
    const currentIndex = zoomLevels.findIndex(level => level >= zoom)
    const nextIndex = Math.min(currentIndex + 1, zoomLevels.length - 1)
    onZoomChange?.(zoomLevels[nextIndex])
  }

  const handleZoomOut = () => {
    const currentIndex = zoomLevels.findIndex(level => level >= zoom)
    const prevIndex = Math.max(currentIndex - 1, 0)
    onZoomChange?.(zoomLevels[prevIndex])
  }

  const handleReset = () => {
    onZoomChange?.(1)
  }

  return (
    <div className="absolute top-4 right-4 flex items-center space-x-2 bg-white rounded-lg shadow-md border border-gray-200 p-2 z-10">
      <button
        onClick={handleZoomOut}
        disabled={zoom <= zoomLevels[0]}
        className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        title="Zoom Out"
      >
        <ZoomOut className="w-4 h-4" />
      </button>
      
      <select
        value={zoom}
        onChange={(e) => onZoomChange?.(parseFloat(e.target.value))}
        className="px-2 py-1 text-sm border border-gray-300 rounded"
      >
        {zoomLevels.map((level) => (
          <option key={level} value={level}>
            {Math.round(level * 100)}%
          </option>
        ))}
      </select>
      
      <button
        onClick={handleZoomIn}
        disabled={zoom >= zoomLevels[zoomLevels.length - 1]}
        className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        title="Zoom In"
      >
        <ZoomIn className="w-4 h-4" />
      </button>
      
      <div className="w-px h-4 bg-gray-300" />
      
      <button
        onClick={handleReset}
        className="p-1 rounded hover:bg-gray-100"
        title="Reset Zoom"
      >
        <RotateCcw className="w-4 h-4" />
      </button>
    </div>
  )
}
