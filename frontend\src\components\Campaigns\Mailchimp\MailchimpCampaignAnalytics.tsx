import React, { useState } from 'react'
import { 
  TrendingUp, Users, Mail, MousePointer, 
  Calendar, Download, Filter, BarChart3,
  Globe, Clock, Target, DollarSign
} from 'lucide-react'
import { 
  LineChart, Line, AreaChart, Area, BarChart, Bar,
  XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  <PERSON><PERSON>hart, Pie, Cell
} from 'recharts'

interface Campaign {
  id: number
  name: string
  performance: {
    sent: number
    delivered: number
    opened: number
    clicked: number
    openRate: number
    clickRate: number
    bounceRate: number
  }
  sent_at?: string
}

interface CampaignStats {
  totalCampaigns: number
  activeCampaigns: number
  totalSent: number
  avgOpenRate: number
  avgClickRate: number
  totalRevenue: number
}

interface MailchimpCampaignAnalyticsProps {
  campaigns: Campaign[]
  stats: CampaignStats
  dateRange: string
  onDateRangeChange: (range: string) => void
}

// Sample data for charts
const performanceOverTime = [
  { date: '2024-01', sent: 12000, opened: 3600, clicked: 720, revenue: 2400 },
  { date: '2024-02', sent: 15000, opened: 4800, clicked: 960, revenue: 3200 },
  { date: '2024-03', sent: 18000, opened: 5940, clicked: 1188, revenue: 3960 },
  { date: '2024-04', sent: 16000, opened: 5280, clicked: 1056, revenue: 3520 },
  { date: '2024-05', sent: 20000, opened: 6800, clicked: 1360, revenue: 4533 },
  { date: '2024-06', sent: 22000, opened: 7700, clicked: 1540, revenue: 5133 }
]

const deviceData = [
  { name: 'Desktop', value: 45, color: '#3B82F6' },
  { name: 'Mobile', value: 40, color: '#10B981' },
  { name: 'Tablet', value: 15, color: '#F59E0B' }
]

const topCampaigns = [
  { name: 'Summer Sale Newsletter', openRate: 35.2, clickRate: 8.7, revenue: 1250 },
  { name: 'Product Launch Announcement', openRate: 42.1, clickRate: 12.3, revenue: 2100 },
  { name: 'Welcome Series Email 1', openRate: 28.9, clickRate: 6.4, revenue: 890 },
  { name: 'Monthly Newsletter', openRate: 31.5, clickRate: 7.8, revenue: 1050 },
  { name: 'Black Friday Promotion', openRate: 48.3, clickRate: 15.2, revenue: 3200 }
]

export const MailchimpCampaignAnalytics: React.FC<MailchimpCampaignAnalyticsProps> = ({
  campaigns,
  stats,
  dateRange,
  onDateRangeChange
}) => {
  const [selectedMetric, setSelectedMetric] = useState<'opens' | 'clicks' | 'revenue'>('opens')
  const [comparisonMode, setComparisonMode] = useState(false)

  const metrics = [
    {
      id: 'total-sent',
      name: 'Total Sent',
      value: stats.totalSent.toLocaleString(),
      change: '+12.5%',
      changeType: 'positive' as const,
      icon: Mail,
      color: 'blue'
    },
    {
      id: 'avg-open-rate',
      name: 'Avg. Open Rate',
      value: `${stats.avgOpenRate.toFixed(1)}%`,
      change: '+2.3%',
      changeType: 'positive' as const,
      icon: TrendingUp,
      color: 'green'
    },
    {
      id: 'avg-click-rate',
      name: 'Avg. Click Rate',
      value: `${stats.avgClickRate.toFixed(1)}%`,
      change: '+1.8%',
      changeType: 'positive' as const,
      icon: MousePointer,
      color: 'purple'
    },
    {
      id: 'total-revenue',
      name: 'Total Revenue',
      value: `$${stats.totalRevenue.toLocaleString()}`,
      change: '+18.2%',
      changeType: 'positive' as const,
      icon: DollarSign,
      color: 'yellow'
    }
  ]

  const getMetricColor = (color: string) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-600',
      green: 'bg-green-100 text-green-600',
      purple: 'bg-purple-100 text-purple-600',
      yellow: 'bg-yellow-100 text-yellow-600'
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Campaign Analytics</h2>
          <p className="text-gray-600 mt-1">Track performance across all your campaigns</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={dateRange}
            onChange={(e) => onDateRangeChange(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="7days">Last 7 days</option>
            <option value="30days">Last 30 days</option>
            <option value="90days">Last 90 days</option>
            <option value="6months">Last 6 months</option>
            <option value="1year">Last year</option>
          </select>
          
          <button className="flex items-center px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <Download className="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric) => {
          const Icon = metric.icon
          return (
            <div key={metric.id} className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{metric.name}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{metric.value}</p>
                </div>
                <div className={`p-3 rounded-full ${getMetricColor(metric.color)}`}>
                  <Icon className="w-6 h-6" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <span className={`font-medium ${
                  metric.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {metric.change}
                </span>
                <span className="text-gray-600 ml-1">from last period</span>
              </div>
            </div>
          )
        })}
      </div>

      {/* Performance Over Time */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Performance Over Time</h3>
          <div className="flex items-center space-x-4">
            <select
              value={selectedMetric}
              onChange={(e) => setSelectedMetric(e.target.value as any)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
            >
              <option value="opens">Opens</option>
              <option value="clicks">Clicks</option>
              <option value="revenue">Revenue</option>
            </select>
            <button
              onClick={() => setComparisonMode(!comparisonMode)}
              className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                comparisonMode
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Compare
            </button>
          </div>
        </div>
        
        <ResponsiveContainer width="100%" height={400}>
          <AreaChart data={performanceOverTime}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            {selectedMetric === 'opens' && (
              <Area
                type="monotone"
                dataKey="opened"
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.1}
                name="Opens"
              />
            )}
            {selectedMetric === 'clicks' && (
              <Area
                type="monotone"
                dataKey="clicked"
                stroke="#10B981"
                fill="#10B981"
                fillOpacity={0.1}
                name="Clicks"
              />
            )}
            {selectedMetric === 'revenue' && (
              <Area
                type="monotone"
                dataKey="revenue"
                stroke="#F59E0B"
                fill="#F59E0B"
                fillOpacity={0.1}
                name="Revenue ($)"
              />
            )}
          </AreaChart>
        </ResponsiveContainer>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Device Breakdown */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Device Breakdown</h3>
          <div className="flex items-center justify-center">
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={deviceData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {deviceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 grid grid-cols-3 gap-4">
            {deviceData.map((item, index) => (
              <div key={index} className="text-center">
                <div 
                  className="w-3 h-3 rounded-full mx-auto mb-1" 
                  style={{ backgroundColor: item.color }}
                ></div>
                <p className="text-sm font-medium text-gray-900">{item.value}%</p>
                <p className="text-xs text-gray-600">{item.name}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Top Performing Campaigns */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Campaigns</h3>
          <div className="space-y-4">
            {topCampaigns.map((campaign, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 text-sm truncate">
                    {campaign.name}
                  </h4>
                  <div className="flex items-center space-x-4 mt-1 text-xs text-gray-600">
                    <span>Open: {campaign.openRate}%</span>
                    <span>Click: {campaign.clickRate}%</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium text-gray-900 text-sm">
                    ${campaign.revenue}
                  </p>
                  <p className="text-xs text-gray-600">Revenue</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Campaign Comparison Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Campaign Performance Comparison</h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Campaign
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Open Rate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Click Rate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Bounce Rate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date Sent
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {campaigns.slice(0, 10).map((campaign) => (
                <tr key={campaign.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                      {campaign.name}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {campaign.performance.sent.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="text-sm text-gray-900">
                        {campaign.performance.openRate.toFixed(1)}%
                      </div>
                      <div className="ml-2 w-16 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${Math.min(campaign.performance.openRate, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="text-sm text-gray-900">
                        {campaign.performance.clickRate.toFixed(1)}%
                      </div>
                      <div className="ml-2 w-16 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${Math.min(campaign.performance.clickRate * 4, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {campaign.performance.bounceRate.toFixed(1)}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {campaign.sent_at ? new Date(campaign.sent_at).toLocaleDateString() : '—'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default MailchimpCampaignAnalytics
