import React, { useState } from 'react'
import { 
  CheckCircle, Clock, Users, Mail, Calendar, 
  Globe, Target, Settings, Edit, Send, AlertTriangle,
  Eye, Smartphone, Monitor, Tablet
} from 'lucide-react'

interface CampaignData {
  name: string
  subject: string
  preheader: string
  fromName: string
  fromEmail: string
  replyTo: string
  type: 'regular' | 'ab_test' | 'automated' | 'rss'
  abTest?: {
    testType: 'subject' | 'from_name' | 'content'
    testPercentage: number
    winnerCriteria: 'opens' | 'clicks'
    testDuration: number
  }
  recipients: {
    lists: number[]
    segments: number[]
    tags: string[]
    totalCount: number
  }
  template: {
    id?: number
    type: 'existing' | 'new' | 'gallery'
    content?: any
  }
  schedule: {
    sendImmediately: boolean
    sendTime?: string
    timezone: string
    sendTimeOptimization: boolean
  }
  tracking: {
    opens: boolean
    clicks: boolean
    googleAnalytics: boolean
    facebookPixel: boolean
  }
}

interface MailchimpCampaignConfirmProps {
  campaignData: CampaignData
  onUpdate: (updates: Partial<CampaignData>) => void
  onNext: () => void
  onPrevious: () => void
  canProceed: boolean
  isLastStep: boolean
  loading: boolean
}

export const MailchimpCampaignConfirm: React.FC<MailchimpCampaignConfirmProps> = ({
  campaignData,
  onUpdate,
  loading
}) => {
  const [showPreview, setShowPreview] = useState(false)
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [sendTestEmail, setSendTestEmail] = useState('')
  const [showTestModal, setShowTestModal] = useState(false)

  const updateSchedule = (scheduleUpdates: Partial<CampaignData['schedule']>) => {
    onUpdate({
      schedule: {
        ...campaignData.schedule,
        ...scheduleUpdates
      }
    })
  }

  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    })
  }

  const getTypeDisplay = (type: string) => {
    switch (type) {
      case 'ab_test':
        return 'A/B Test Campaign'
      case 'automated':
        return 'Automated Campaign'
      case 'rss':
        return 'RSS Campaign'
      default:
        return 'Regular Campaign'
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Review & Confirm</h2>
        <p className="text-gray-600">
          Review your campaign details and schedule your send
        </p>
      </div>

      {/* Campaign Summary */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Campaign Summary</h3>
        </div>
        
        <div className="p-6 space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">Campaign Details</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Name:</span>
                  <span className="font-medium">{campaignData.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Type:</span>
                  <span className="font-medium">{getTypeDisplay(campaignData.type)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Subject:</span>
                  <span className="font-medium">{campaignData.subject}</span>
                </div>
                {campaignData.preheader && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Preview:</span>
                    <span className="font-medium">{campaignData.preheader}</span>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">From Information</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">From Name:</span>
                  <span className="font-medium">{campaignData.fromName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">From Email:</span>
                  <span className="font-medium">{campaignData.fromEmail}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Reply-To:</span>
                  <span className="font-medium">{campaignData.replyTo || campaignData.fromEmail}</span>
                </div>
              </div>
            </div>
          </div>

          {/* A/B Test Info */}
          {campaignData.type === 'ab_test' && campaignData.abTest && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-green-900 mb-3">A/B Test Configuration</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-green-700">Testing:</span>
                  <p className="font-medium text-green-900 capitalize">
                    {campaignData.abTest.testType.replace('_', ' ')}
                  </p>
                </div>
                <div>
                  <span className="text-green-700">Test Size:</span>
                  <p className="font-medium text-green-900">{campaignData.abTest.testPercentage}%</p>
                </div>
                <div>
                  <span className="text-green-700">Winner Criteria:</span>
                  <p className="font-medium text-green-900 capitalize">
                    {campaignData.abTest.winnerCriteria}
                  </p>
                </div>
                <div>
                  <span className="text-green-700">Duration:</span>
                  <p className="font-medium text-green-900">{campaignData.abTest.testDuration}h</p>
                </div>
              </div>
            </div>
          )}

          {/* Recipients */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Recipients</h4>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Users className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-blue-900">
                      {campaignData.recipients.totalCount.toLocaleString()} recipients
                    </p>
                    <p className="text-sm text-blue-700">
                      {campaignData.recipients.lists.length} lists, {campaignData.recipients.segments.length} segments, {campaignData.recipients.tags.length} tags
                    </p>
                  </div>
                </div>
                <CheckCircle className="w-5 h-5 text-green-500" />
              </div>
            </div>
          </div>

          {/* Template */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Template</h4>
            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="font-medium text-gray-900">
                    {campaignData.template.type === 'existing' ? 'Existing Template' : 
                     campaignData.template.type === 'gallery' ? 'Gallery Template' : 'New Template'}
                  </p>
                  <p className="text-sm text-gray-600">Template ID: {campaignData.template.id}</p>
                </div>
              </div>
              <button
                onClick={() => setShowPreview(true)}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                <Eye className="w-4 h-4 inline mr-1" />
                Preview
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Scheduling */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Schedule & Send</h3>
        </div>
        
        <div className="p-6 space-y-6">
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="radio"
                id="send-now"
                name="schedule"
                checked={campaignData.schedule.sendImmediately}
                onChange={() => updateSchedule({ sendImmediately: true })}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <label htmlFor="send-now" className="ml-3 text-sm text-gray-700">
                Send immediately
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="radio"
                id="schedule-later"
                name="schedule"
                checked={!campaignData.schedule.sendImmediately}
                onChange={() => updateSchedule({ sendImmediately: false })}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <label htmlFor="schedule-later" className="ml-3 text-sm text-gray-700">
                Schedule for later
              </label>
            </div>

            {!campaignData.schedule.sendImmediately && (
              <div className="ml-7 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Send Date & Time
                  </label>
                  <input
                    type="datetime-local"
                    value={campaignData.schedule.sendTime || ''}
                    onChange={(e) => updateSchedule({ sendTime: e.target.value })}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Timezone
                  </label>
                  <select
                    value={campaignData.schedule.timezone}
                    onChange={(e) => updateSchedule({ timezone: e.target.value })}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="America/New_York">Eastern Time (ET)</option>
                    <option value="America/Chicago">Central Time (CT)</option>
                    <option value="America/Denver">Mountain Time (MT)</option>
                    <option value="America/Los_Angeles">Pacific Time (PT)</option>
                    <option value="UTC">UTC</option>
                  </select>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="send-time-optimization"
                    checked={campaignData.schedule.sendTimeOptimization}
                    onChange={(e) => updateSchedule({ sendTimeOptimization: e.target.checked })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="send-time-optimization" className="ml-3 text-sm text-gray-700">
                    Use send time optimization
                  </label>
                </div>
              </div>
            )}
          </div>

          {/* Send Summary */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Calendar className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-900">
                  {campaignData.schedule.sendImmediately ? 'Ready to Send' : 'Scheduled Send'}
                </h4>
                <p className="text-sm text-yellow-800 mt-1">
                  {campaignData.schedule.sendImmediately 
                    ? `Your campaign will be sent immediately to ${campaignData.recipients.totalCount.toLocaleString()} recipients.`
                    : campaignData.schedule.sendTime
                      ? `Your campaign is scheduled to send on ${formatDateTime(campaignData.schedule.sendTime)}.`
                      : 'Please select a send date and time.'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Test Email */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Send Test Email</h3>
        <p className="text-gray-600 mb-4">
          Send a test email to yourself to see how your campaign will look
        </p>
        <button
          onClick={() => setShowTestModal(true)}
          className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
        >
          <Send className="w-4 h-4 mr-2 inline" />
          Send Test Email
        </button>
      </div>

      {/* Final Checklist */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Pre-Send Checklist</h3>
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span className="text-sm text-gray-700">Campaign details configured</span>
          </div>
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span className="text-sm text-gray-700">Recipients selected ({campaignData.recipients.totalCount.toLocaleString()})</span>
          </div>
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span className="text-sm text-gray-700">Template chosen</span>
          </div>
          <div className="flex items-center space-x-3">
            {campaignData.schedule.sendImmediately || campaignData.schedule.sendTime ? (
              <CheckCircle className="w-5 h-5 text-green-500" />
            ) : (
              <AlertTriangle className="w-5 h-5 text-yellow-500" />
            )}
            <span className="text-sm text-gray-700">
              Send schedule {campaignData.schedule.sendImmediately || campaignData.schedule.sendTime ? 'configured' : 'needs configuration'}
            </span>
          </div>
        </div>
      </div>

      {/* Test Email Modal */}
      {showTestModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Send Test Email</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  value={sendTestEmail}
                  onChange={(e) => setSendTestEmail(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter email address"
                />
              </div>
              <div className="flex items-center justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowTestModal(false)
                    setSendTestEmail('')
                  }}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  disabled={!sendTestEmail.trim()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Send Test
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default MailchimpCampaignConfirm
