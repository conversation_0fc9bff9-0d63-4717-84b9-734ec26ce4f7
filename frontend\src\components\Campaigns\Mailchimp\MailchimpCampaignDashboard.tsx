import React from 'react'
import { 
  TrendingUp, Users, Mail, DollarSign, Clock, 
  Target, Zap, BarChart3, Plus, ArrowRight,
  Calendar, CheckCircle, AlertCircle, Play
} from 'lucide-react'
import { LineChart, Line, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts'

interface CampaignStats {
  totalCampaigns: number
  activeCampaigns: number
  totalSent: number
  avgOpenRate: number
  avgClickRate: number
  totalRevenue: number
}

interface Campaign {
  id: number
  name: string
  subject: string
  type: 'regular' | 'ab_test' | 'automated' | 'rss'
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'paused' | 'cancelled'
  performance: {
    sent: number
    delivered: number
    opened: number
    clicked: number
    openRate: number
    clickRate: number
  }
  created_at: string
  sent_at?: string
  scheduled_at?: string
}

interface MailchimpCampaignDashboardProps {
  stats: CampaignStats
  recentCampaigns: Campaign[]
  onCreateCampaign: (type?: 'regular' | 'ab_test' | 'automated' | 'rss') => void
  onViewCampaign: (id: number) => void
  loading: boolean
}

// Sample data for charts
const performanceData = [
  { name: 'Jan', sent: 4000, opened: 2400, clicked: 800 },
  { name: 'Feb', sent: 3000, opened: 1398, clicked: 600 },
  { name: 'Mar', sent: 2000, opened: 1800, clicked: 720 },
  { name: 'Apr', sent: 2780, opened: 1908, clicked: 763 },
  { name: 'May', sent: 1890, opened: 1200, clicked: 480 },
  { name: 'Jun', sent: 2390, opened: 1430, clicked: 572 }
]

const campaignTypeData = [
  { name: 'Regular', value: 65, color: '#3B82F6' },
  { name: 'A/B Test', value: 20, color: '#10B981' },
  { name: 'Automated', value: 12, color: '#F59E0B' },
  { name: 'RSS', value: 3, color: '#EF4444' }
]

export const MailchimpCampaignDashboard: React.FC<MailchimpCampaignDashboardProps> = ({
  stats,
  recentCampaigns,
  onCreateCampaign,
  onViewCampaign,
  loading
}) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'sending':
        return <Play className="w-4 h-4 text-blue-500" />
      case 'scheduled':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'draft':
        return <AlertCircle className="w-4 h-4 text-gray-500" />
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-green-100 text-green-800'
      case 'sending':
        return 'bg-blue-100 text-blue-800'
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800'
      case 'draft':
        return 'bg-gray-100 text-gray-800'
      case 'paused':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Loading skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white p-6 rounded-lg border border-gray-200 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={() => onCreateCampaign('regular')}
            className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors group"
          >
            <div className="text-center">
              <Mail className="w-8 h-8 text-gray-400 group-hover:text-blue-500 mx-auto mb-2" />
              <h3 className="font-medium text-gray-900">Regular Campaign</h3>
              <p className="text-sm text-gray-600">Send to your audience</p>
            </div>
          </button>

          <button
            onClick={() => onCreateCampaign('ab_test')}
            className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-400 hover:bg-green-50 transition-colors group"
          >
            <div className="text-center">
              <Target className="w-8 h-8 text-gray-400 group-hover:text-green-500 mx-auto mb-2" />
              <h3 className="font-medium text-gray-900">A/B Test</h3>
              <p className="text-sm text-gray-600">Test different versions</p>
            </div>
          </button>

          <button
            onClick={() => onCreateCampaign('automated')}
            className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-yellow-400 hover:bg-yellow-50 transition-colors group"
          >
            <div className="text-center">
              <Zap className="w-8 h-8 text-gray-400 group-hover:text-yellow-500 mx-auto mb-2" />
              <h3 className="font-medium text-gray-900">Automated</h3>
              <p className="text-sm text-gray-600">Set up automation</p>
            </div>
          </button>

          <button
            onClick={() => onCreateCampaign('rss')}
            className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-colors group"
          >
            <div className="text-center">
              <BarChart3 className="w-8 h-8 text-gray-400 group-hover:text-purple-500 mx-auto mb-2" />
              <h3 className="font-medium text-gray-900">RSS Campaign</h3>
              <p className="text-sm text-gray-600">Auto-send from RSS</p>
            </div>
          </button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Campaigns</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalCampaigns.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Mail className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-green-600">+12%</span>
            <span className="text-gray-600 ml-1">from last month</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Campaigns</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeCampaigns}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <Play className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <span className="text-gray-600">Currently running</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg. Open Rate</p>
              <p className="text-2xl font-bold text-gray-900">{stats.avgOpenRate.toFixed(1)}%</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-full">
              <TrendingUp className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-green-600">+2.3%</span>
            <span className="text-gray-600 ml-1">from last month</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg. Click Rate</p>
              <p className="text-2xl font-bold text-gray-900">{stats.avgClickRate.toFixed(1)}%</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <Target className="w-6 h-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-sm">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-green-600">+1.8%</span>
            <span className="text-gray-600 ml-1">from last month</span>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Chart */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Campaign Performance</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={performanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Area type="monotone" dataKey="sent" stackId="1" stroke="#3B82F6" fill="#3B82F6" fillOpacity={0.1} />
              <Area type="monotone" dataKey="opened" stackId="2" stroke="#10B981" fill="#10B981" fillOpacity={0.3} />
              <Area type="monotone" dataKey="clicked" stackId="3" stroke="#F59E0B" fill="#F59E0B" fillOpacity={0.5} />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Campaign Types */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Campaign Types</h3>
          <div className="flex items-center justify-center">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={campaignTypeData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {campaignTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-4">
            {campaignTypeData.map((item, index) => (
              <div key={index} className="flex items-center">
                <div 
                  className="w-3 h-3 rounded-full mr-2" 
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-sm text-gray-600">{item.name}: {item.value}%</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Campaigns */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Recent Campaigns</h3>
            <button className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
              View All
              <ArrowRight className="w-4 h-4 ml-1" />
            </button>
          </div>
        </div>
        
        <div className="divide-y divide-gray-200">
          {recentCampaigns.length === 0 ? (
            <div className="p-8 text-center">
              <Mail className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">No campaigns yet</h4>
              <p className="text-gray-600 mb-4">Create your first campaign to get started</p>
              <button
                onClick={() => onCreateCampaign()}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Create Campaign
              </button>
            </div>
          ) : (
            recentCampaigns.map((campaign) => (
              <div key={campaign.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(campaign.status)}
                      <h4 className="font-medium text-gray-900">{campaign.name}</h4>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(campaign.status)}`}>
                        {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{campaign.subject}</p>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      <span>Sent: {campaign.performance.sent.toLocaleString()}</span>
                      <span>Open Rate: {campaign.performance.openRate.toFixed(1)}%</span>
                      <span>Click Rate: {campaign.performance.clickRate.toFixed(1)}%</span>
                    </div>
                  </div>
                  <button
                    onClick={() => onViewCampaign(campaign.id)}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    View Details
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}

export default MailchimpCampaignDashboard
