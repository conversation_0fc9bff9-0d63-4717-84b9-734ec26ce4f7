import React, { useState, useMemo, useCallback } from 'react'
import { useDrag } from 'react-dnd'
import { Search, Type, Image, Button, Minus, Square, Layout, Header, Footer, ShoppingBag, Share2 } from 'lucide-react'
import { TemplateComponent } from '../../types/templateEditor'
import { useVirtualization, usePerformanceMonitor } from '../../hooks/useVirtualization'

interface ComponentDefinition {
  id: string
  name: string
  icon: React.ReactNode
  category: string
  description: string
  defaultContent: Record<string, any>
  defaultStyles: Record<string, any>
  componentType: TemplateComponent['type']
  tags: string[]
}

const COMPONENT_DEFINITIONS: ComponentDefinition[] = [
  {
    id: 'text',
    name: 'Text',
    icon: <Type className="w-4 h-4" />,
    category: 'Basic',
    description: 'Add text content',
    defaultContent: { text: 'Your text here...' },
    defaultStyles: { fontSize: '16px', color: '#000000', padding: '8px' },
    componentType: 'text',
    tags: ['text', 'content', 'paragraph']
  },
  {
    id: 'heading',
    name: 'Heading',
    icon: <Type className="w-4 h-4" />,
    category: 'Basic',
    description: 'Add heading text',
    defaultContent: { text: 'Your Heading', level: 'h2' },
    defaultStyles: { fontSize: '24px', fontWeight: 'bold', color: '#000000', padding: '8px' },
    componentType: 'heading',
    tags: ['heading', 'title', 'h1', 'h2', 'h3']
  },
  {
    id: 'image',
    name: 'Image',
    icon: <Image className="w-4 h-4" />,
    category: 'Media',
    description: 'Add an image',
    defaultContent: { src: 'https://via.placeholder.com/300x200', alt: 'Image' },
    defaultStyles: { width: '300px', height: '200px', objectFit: 'cover' },
    componentType: 'image',
    tags: ['image', 'photo', 'picture', 'media']
  },
  {
    id: 'button',
    name: 'Button',
    icon: <Button className="w-4 h-4" />,
    category: 'Interactive',
    description: 'Add a clickable button',
    defaultContent: { text: 'Click Here', href: '#' },
    defaultStyles: { 
      backgroundColor: '#3b82f6', 
      color: '#ffffff', 
      padding: '12px 24px', 
      borderRadius: '6px',
      textDecoration: 'none',
      display: 'inline-block'
    },
    componentType: 'button',
    tags: ['button', 'link', 'cta', 'action']
  },
  {
    id: 'divider',
    name: 'Divider',
    icon: <Minus className="w-4 h-4" />,
    category: 'Layout',
    description: 'Add a horizontal line',
    defaultContent: {},
    defaultStyles: { 
      height: '1px', 
      backgroundColor: '#e5e7eb', 
      border: 'none', 
      margin: '16px 0' 
    },
    componentType: 'divider',
    tags: ['divider', 'separator', 'line', 'hr']
  },
  {
    id: 'spacer',
    name: 'Spacer',
    icon: <Square className="w-4 h-4" />,
    category: 'Layout',
    description: 'Add vertical spacing',
    defaultContent: {},
    defaultStyles: { height: '32px', width: '100%' },
    componentType: 'spacer',
    tags: ['spacer', 'space', 'gap', 'margin']
  },
  {
    id: 'container',
    name: 'Container',
    icon: <Layout className="w-4 h-4" />,
    category: 'Layout',
    description: 'Group other components',
    defaultContent: {},
    defaultStyles: { 
      padding: '16px', 
      border: '1px solid #e5e7eb', 
      borderRadius: '8px',
      backgroundColor: '#ffffff'
    },
    componentType: 'container',
    tags: ['container', 'wrapper', 'group', 'section']
  },
  {
    id: 'header',
    name: 'Header',
    icon: <Header className="w-4 h-4" />,
    category: 'Sections',
    description: 'Email header section',
    defaultContent: { title: 'Your Company', logo: '' },
    defaultStyles: { 
      padding: '24px', 
      backgroundColor: '#f8fafc', 
      textAlign: 'center',
      borderBottom: '1px solid #e5e7eb'
    },
    componentType: 'header',
    tags: ['header', 'top', 'logo', 'company']
  },
  {
    id: 'footer',
    name: 'Footer',
    icon: <Footer className="w-4 h-4" />,
    category: 'Sections',
    description: 'Email footer section',
    defaultContent: { 
      text: '© 2024 Your Company. All rights reserved.',
      links: []
    },
    defaultStyles: { 
      padding: '24px', 
      backgroundColor: '#f8fafc', 
      textAlign: 'center',
      borderTop: '1px solid #e5e7eb',
      fontSize: '14px',
      color: '#6b7280'
    },
    componentType: 'footer',
    tags: ['footer', 'bottom', 'copyright', 'links']
  }
]

const CATEGORIES = ['All', 'Basic', 'Media', 'Interactive', 'Layout', 'Sections']

interface OptimizedComponentLibraryProps {
  onComponentAdd: (component: TemplateComponent) => void
}

const DraggableComponent: React.FC<{
  component: ComponentDefinition
  onAdd: (component: ComponentDefinition) => void
}> = React.memo(({ component, onAdd }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'component',
    item: { component },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }))

  return (
    <div
      ref={drag}
      className={`p-3 border border-gray-200 rounded-lg cursor-move hover:border-primary-300 hover:shadow-sm transition-all duration-200 ${
        isDragging ? 'opacity-50' : ''
      }`}
      onClick={() => onAdd(component)}
    >
      <div className="flex items-center space-x-3">
        <div className="flex-shrink-0 text-gray-600">
          {component.icon}
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-900 truncate">
            {component.name}
          </h4>
          <p className="text-xs text-gray-500 truncate">
            {component.description}
          </p>
        </div>
      </div>
    </div>
  )
})

DraggableComponent.displayName = 'DraggableComponent'

export const OptimizedComponentLibrary: React.FC<OptimizedComponentLibraryProps> = ({
  onComponentAdd
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const performanceMetrics = usePerformanceMonitor('ComponentLibrary')

  const filteredComponents = useMemo(() => {
    return COMPONENT_DEFINITIONS.filter(component => {
      const matchesSearch = searchTerm === '' || 
        component.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        component.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        component.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      
      const matchesCategory = selectedCategory === 'All' || component.category === selectedCategory
      
      return matchesSearch && matchesCategory
    })
  }, [searchTerm, selectedCategory])

  const handleComponentAdd = useCallback((componentDef: ComponentDefinition) => {
    const newComponent: TemplateComponent = {
      id: `component-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: componentDef.componentType,
      content: { ...componentDef.defaultContent },
      styles: { ...componentDef.defaultStyles },
      position: { x: 0, y: 0 }
    }
    onComponentAdd(newComponent)
  }, [onComponentAdd])

  // Virtualization for large component lists
  const containerHeight = 400 // Adjust based on your layout
  const itemHeight = 80
  
  const {
    virtualizedItems,
    totalHeight,
    handleScroll
  } = useVirtualization({
    items: filteredComponents,
    itemHeight,
    containerHeight,
    overscan: 3
  })

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-3">Components</h2>
        
        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search components..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
          />
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-1">
          {CATEGORIES.map(category => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-3 py-1 text-xs rounded-full transition-colors ${
                selectedCategory === category
                  ? 'bg-primary-100 text-primary-700 border border-primary-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* Component List */}
      <div className="flex-1 overflow-hidden">
        {filteredComponents.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <p>No components found</p>
            <p className="text-sm">Try adjusting your search or category filter</p>
          </div>
        ) : (
          <div 
            className="h-full overflow-auto"
            onScroll={handleScroll}
          >
            <div 
              className="relative"
              style={{ height: totalHeight }}
            >
              {virtualizedItems.map(({ item: component, style }) => (
                <div key={component.id} style={style}>
                  <div className="p-2">
                    <DraggableComponent
                      component={component}
                      onAdd={handleComponentAdd}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Performance Info (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="p-2 border-t border-gray-200 text-xs text-gray-500">
          <div>Renders: {performanceMetrics.renderCount}</div>
          <div>Avg: {performanceMetrics.averageRenderTime.toFixed(1)}ms</div>
          <div>Components: {filteredComponents.length}</div>
        </div>
      )}
    </div>
  )
}

export default OptimizedComponentLibrary
