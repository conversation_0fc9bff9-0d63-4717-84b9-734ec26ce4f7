import React, { useState } from 'react'
import { TemplateComponent } from '../../types/templateEditor'
import {
  Type,
  Palette,
  Layout,
  Smartphone,
  Monitor,
  Tablet,
  ChevronDown,
  ChevronRight,
  Image,
  Plus,
  Minus,
  RotateCcw
} from 'lucide-react'
import { ColorPicker } from './ColorPicker'
import { FontSelector } from './FontSelector'
import { SpacingControls } from './SpacingControls'
import { ImageUploadModal } from './ImageUploadModal'

interface PropertiesPanelProps {
  selectedComponent: TemplateComponent | null
  onComponentUpdate: (updates: Partial<TemplateComponent>) => void
  previewMode: 'desktop' | 'tablet' | 'mobile'
  onPreviewModeChange: (mode: 'desktop' | 'tablet' | 'mobile') => void
  templateSettings?: any
  onTemplateSettingsChange?: (settings: any) => void
}

export const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
  selectedComponent,
  onComponentUpdate,
  previewMode,
  onPreviewModeChange,
  templateSettings: _templateSettings,
  onTemplateSettingsChange: _onTemplateSettingsChange
}) => {
  const [expandedSections, setExpandedSections] = useState<string[]>([
    'content', 'typography', 'layout', 'appearance'
  ])
  const [showImageModal, setShowImageModal] = useState(false)

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    )
  }

  const updateStyles = (styleUpdates: Record<string, any>) => {
    if (!selectedComponent) return
    
    onComponentUpdate({
      styles: {
        ...selectedComponent.styles,
        ...styleUpdates
      }
    })
  }

  const updateContent = (contentUpdates: Record<string, any>) => {
    if (!selectedComponent) return
    
    onComponentUpdate({
      content: {
        ...selectedComponent.content,
        ...contentUpdates
      }
    })
  }

  const PropertySection: React.FC<{
    id: string
    title: string
    icon: React.ReactNode
    children: React.ReactNode
  }> = ({ id, title, icon, children }) => (
    <div className="border border-gray-200 rounded-lg mb-3">
      <button
        onClick={() => toggleSection(id)}
        className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50"
      >
        <div className="flex items-center space-x-2">
          {icon}
          <span className="font-medium text-gray-700">{title}</span>
        </div>
        {expandedSections.includes(id) ? (
          <ChevronDown className="w-4 h-4 text-gray-500" />
        ) : (
          <ChevronRight className="w-4 h-4 text-gray-500" />
        )}
      </button>
      
      {expandedSections.includes(id) && (
        <div className="p-3 pt-0 space-y-3">
          {children}
        </div>
      )}
    </div>
  )

  if (!selectedComponent) {
    return (
      <div className="w-80 bg-white border-l border-gray-200 p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Properties</h3>
        
        {/* Device Preview Controls */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Preview Mode
          </label>
          <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => onPreviewModeChange('desktop')}
              className={`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                previewMode === 'desktop'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Monitor className="w-4 h-4 mr-1" />
              Desktop
            </button>
            <button
              onClick={() => onPreviewModeChange('tablet')}
              className={`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                previewMode === 'tablet'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Tablet className="w-4 h-4 mr-1" />
              Tablet
            </button>
            <button
              onClick={() => onPreviewModeChange('mobile')}
              className={`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                previewMode === 'mobile'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Smartphone className="w-4 h-4 mr-1" />
              Mobile
            </button>
          </div>
        </div>

        <div className="text-center text-gray-500 py-8">
          <Layout className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p className="text-sm">Select an element to edit its properties</p>
        </div>
      </div>
    )
  }

  return (
    <div className="w-80 bg-white border-l border-gray-200 p-4 overflow-y-auto">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Properties</h3>
      
      {/* Device Preview Controls */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Preview Mode
        </label>
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => onPreviewModeChange('desktop')}
            className={`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${
              previewMode === 'desktop'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Monitor className="w-4 h-4 mr-1" />
            Desktop
          </button>
          <button
            onClick={() => onPreviewModeChange('tablet')}
            className={`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${
              previewMode === 'tablet'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Tablet className="w-4 h-4 mr-1" />
            Tablet
          </button>
          <button
            onClick={() => onPreviewModeChange('mobile')}
            className={`flex-1 flex items-center justify-center py-2 px-3 rounded-md text-sm font-medium transition-colors ${
              previewMode === 'mobile'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Smartphone className="w-4 h-4 mr-1" />
            Mobile
          </button>
        </div>
      </div>

      {/* Component Info */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="text-sm font-medium text-gray-900 capitalize">
          {selectedComponent.type} Element
        </div>
        <div className="text-xs text-gray-500">
          ID: {selectedComponent.id}
        </div>
      </div>

      {/* Content Properties */}
      {(selectedComponent.type === 'text' || selectedComponent.type === 'heading' || selectedComponent.type === 'button') && (
        <PropertySection id="content" title="Content" icon={<Type className="w-4 h-4" />}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Text
            </label>
            <textarea
              value={selectedComponent.content?.text || ''}
              onChange={(e) => updateContent({ text: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              rows={3}
            />
          </div>

          {selectedComponent.type === 'button' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Link URL
              </label>
              <input
                type="url"
                value={selectedComponent.content?.href || ''}
                onChange={(e) => updateContent({ href: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                placeholder="https://example.com"
              />
            </div>
          )}
        </PropertySection>
      )}

      {/* Image Properties */}
      {selectedComponent.type === 'image' && (
        <PropertySection id="content" title="Image" icon={<Image className="w-4 h-4" />}>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Image Source
            </label>
            <div className="flex space-x-2">
              <input
                type="url"
                value={selectedComponent.content?.src || ''}
                onChange={(e) => updateContent({ src: e.target.value })}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                placeholder="https://example.com/image.jpg"
              />
              <button
                onClick={() => setShowImageModal(true)}
                className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                Upload
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Alt Text
            </label>
            <input
              type="text"
              value={selectedComponent.content?.alt || ''}
              onChange={(e) => updateContent({ alt: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              placeholder="Image description"
            />
          </div>
        </PropertySection>
      )}

      {/* Typography Properties */}
      <PropertySection id="typography" title="Typography" icon={<Type className="w-4 h-4" />}>
        <FontSelector
          value={selectedComponent.styles?.fontFamily || 'Arial, sans-serif'}
          onChange={(fontFamily) => updateStyles({ fontFamily })}
        />
        
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Size
            </label>
            <input
              type="number"
              value={parseInt(selectedComponent.styles?.fontSize || '16')}
              onChange={(e) => updateStyles({ fontSize: `${e.target.value}px` })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Weight
            </label>
            <select
              value={selectedComponent.styles?.fontWeight || 'normal'}
              onChange={(e) => updateStyles({ fontWeight: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="normal">Normal</option>
              <option value="bold">Bold</option>
              <option value="lighter">Light</option>
              <option value="bolder">Bolder</option>
            </select>
          </div>
        </div>

        <ColorPicker
          label="Text Color"
          value={selectedComponent.styles?.color || '#000000'}
          onChange={(color) => updateStyles({ color })}
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Text Align
          </label>
          <select
            value={selectedComponent.styles?.textAlign || 'left'}
            onChange={(e) => updateStyles({ textAlign: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="left">Left</option>
            <option value="center">Center</option>
            <option value="right">Right</option>
            <option value="justify">Justify</option>
          </select>
        </div>
      </PropertySection>

      {/* Layout Properties */}
      <PropertySection id="layout" title="Layout & Spacing" icon={<Layout className="w-4 h-4" />}>
        <SpacingControls
          padding={selectedComponent.styles?.padding}
          margin={selectedComponent.styles?.margin}
          onPaddingChange={(padding) => updateStyles({ padding })}
          onMarginChange={(margin) => updateStyles({ margin })}
        />

        {/* Size Controls */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Size Controls
          </label>
          <div className="grid grid-cols-2 gap-2 mb-3">
            <button
              onClick={() => {
                const currentWidth = parseInt(selectedComponent.styles?.width || '100')
                updateStyles({ width: `${Math.max(10, currentWidth - 10)}px` })
              }}
              className="flex items-center justify-center px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm"
            >
              <Minus className="w-4 h-4 mr-1" />
              Smaller
            </button>
            <button
              onClick={() => {
                const currentWidth = parseInt(selectedComponent.styles?.width || '100')
                updateStyles({ width: `${currentWidth + 10}px` })
              }}
              className="flex items-center justify-center px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm"
            >
              <Plus className="w-4 h-4 mr-1" />
              Larger
            </button>
          </div>
          <button
            onClick={() => {
              updateStyles({
                width: selectedComponent.type === 'image' ? '300px' : 'auto',
                height: selectedComponent.type === 'image' ? '200px' : 'auto'
              })
            }}
            className="w-full flex items-center justify-center px-3 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-md text-sm"
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            Reset Size
          </button>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Width
            </label>
            <input
              type="text"
              value={selectedComponent.styles?.width || 'auto'}
              onChange={(e) => updateStyles({ width: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              placeholder="auto, 100px, 50%"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Height
            </label>
            <input
              type="text"
              value={selectedComponent.styles?.height || 'auto'}
              onChange={(e) => updateStyles({ height: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              placeholder="auto, 100px, 50%"
            />
          </div>
        </div>
      </PropertySection>

      {/* Appearance Properties */}
      <PropertySection id="appearance" title="Appearance" icon={<Palette className="w-4 h-4" />}>
        <ColorPicker
          label="Background Color"
          value={selectedComponent.styles?.backgroundColor || 'transparent'}
          onChange={(backgroundColor) => updateStyles({ backgroundColor })}
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Border Radius
          </label>
          <input
            type="number"
            value={parseInt(selectedComponent.styles?.borderRadius || '0')}
            onChange={(e) => updateStyles({ borderRadius: `${e.target.value}px` })}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Opacity
          </label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={selectedComponent.styles?.opacity || 1}
            onChange={(e) => updateStyles({ opacity: parseFloat(e.target.value) })}
            className="w-full"
          />
          <div className="text-xs text-gray-500 text-center">
            {Math.round((selectedComponent.styles?.opacity || 1) * 100)}%
          </div>
        </div>
      </PropertySection>

      {/* Image Upload Modal */}
      <ImageUploadModal
        isOpen={showImageModal}
        onClose={() => setShowImageModal(false)}
        onImageSelect={(imageUrl) => {
          updateContent({ src: imageUrl })
          setShowImageModal(false)
        }}
      />
    </div>
  )
}
