import React, { useState, useEffect, useMemo } from 'react'
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell, Area, AreaChart } from 'recharts'
import { TrendingUp, TrendingDown, Users, Mail, Eye, MousePointer, Calendar, Filter } from 'lucide-react'

interface AnalyticsData {
  campaigns: CampaignAnalytics[]
  templates: TemplateAnalytics[]
  overview: OverviewMetrics
  engagement: EngagementMetrics[]
  performance: PerformanceMetrics[]
}

interface CampaignAnalytics {
  id: string
  name: string
  sent: number
  delivered: number
  opened: number
  clicked: number
  bounced: number
  unsubscribed: number
  revenue: number
  sentDate: string
  templateId: string
}

interface TemplateAnalytics {
  id: string
  name: string
  usageCount: number
  avgOpenRate: number
  avgClickRate: number
  avgConversionRate: number
  lastUsed: string
  performance: 'high' | 'medium' | 'low'
}

interface OverviewMetrics {
  totalCampaigns: number
  totalSent: number
  avgOpenRate: number
  avgClickRate: number
  totalRevenue: number
  activeTemplates: number
  trends: {
    campaigns: number
    openRate: number
    clickRate: number
    revenue: number
  }
}

interface EngagementMetrics {
  date: string
  opens: number
  clicks: number
  unsubscribes: number
  bounces: number
}

interface PerformanceMetrics {
  metric: string
  value: number
  change: number
  trend: 'up' | 'down' | 'stable'
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']

const AnalyticsDashboard: React.FC = () => {
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [dateRange, setDateRange] = useState('30d')
  const [selectedMetric, setSelectedMetric] = useState('openRate')

  useEffect(() => {
    fetchAnalyticsData()
  }, [dateRange])

  const fetchAnalyticsData = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const mockData: AnalyticsData = {
        campaigns: [
          {
            id: '1',
            name: 'Summer Sale 2024',
            sent: 10000,
            delivered: 9800,
            opened: 2940,
            clicked: 588,
            bounced: 200,
            unsubscribed: 15,
            revenue: 15000,
            sentDate: '2024-01-15',
            templateId: 'template1'
          },
          {
            id: '2',
            name: 'Product Launch',
            sent: 8500,
            delivered: 8300,
            opened: 2490,
            clicked: 498,
            bounced: 200,
            unsubscribed: 12,
            revenue: 12000,
            sentDate: '2024-01-10',
            templateId: 'template2'
          }
        ],
        templates: [
          {
            id: 'template1',
            name: 'Newsletter Template',
            usageCount: 25,
            avgOpenRate: 30,
            avgClickRate: 6,
            avgConversionRate: 2.5,
            lastUsed: '2024-01-15',
            performance: 'high'
          },
          {
            id: 'template2',
            name: 'Promotional Template',
            usageCount: 18,
            avgOpenRate: 28,
            avgClickRate: 5.8,
            avgConversionRate: 2.1,
            lastUsed: '2024-01-10',
            performance: 'medium'
          }
        ],
        overview: {
          totalCampaigns: 45,
          totalSent: 125000,
          avgOpenRate: 29.2,
          avgClickRate: 5.9,
          totalRevenue: 85000,
          activeTemplates: 12,
          trends: {
            campaigns: 12,
            openRate: 2.5,
            clickRate: 0.8,
            revenue: 15
          }
        },
        engagement: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          opens: Math.floor(Math.random() * 1000) + 500,
          clicks: Math.floor(Math.random() * 200) + 50,
          unsubscribes: Math.floor(Math.random() * 10) + 1,
          bounces: Math.floor(Math.random() * 50) + 10
        })),
        performance: [
          { metric: 'Open Rate', value: 29.2, change: 2.5, trend: 'up' },
          { metric: 'Click Rate', value: 5.9, change: 0.8, trend: 'up' },
          { metric: 'Bounce Rate', value: 2.1, change: -0.3, trend: 'down' },
          { metric: 'Unsubscribe Rate', value: 0.15, change: -0.05, trend: 'down' }
        ]
      }
      
      setData(mockData)
    } catch (error) {
      console.error('Failed to fetch analytics data:', error)
    } finally {
      setLoading(false)
    }
  }

  const chartData = useMemo(() => {
    if (!data) return []
    
    return data.engagement.map(item => ({
      date: new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      openRate: ((item.opens / (item.opens + item.clicks + item.unsubscribes + item.bounces)) * 100).toFixed(1),
      clickRate: ((item.clicks / (item.opens + item.clicks + item.unsubscribes + item.bounces)) * 100).toFixed(1),
      opens: item.opens,
      clicks: item.clicks
    }))
  }, [data])

  const templatePerformanceData = useMemo(() => {
    if (!data) return []
    
    return data.templates.map(template => ({
      name: template.name.length > 15 ? template.name.substring(0, 15) + '...' : template.name,
      openRate: template.avgOpenRate,
      clickRate: template.avgClickRate,
      usage: template.usageCount
    }))
  }, [data])

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-500">Failed to load analytics data</p>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
        <div className="flex items-center space-x-4">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
            <Filter className="w-4 h-4" />
            <span>Filter</span>
          </button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Campaigns</p>
              <p className="text-2xl font-bold text-gray-900">{data.overview.totalCampaigns}</p>
            </div>
            <div className="flex items-center space-x-1 text-green-600">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm font-medium">+{data.overview.trends.campaigns}%</span>
            </div>
          </div>
          <div className="mt-2">
            <Mail className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Open Rate</p>
              <p className="text-2xl font-bold text-gray-900">{data.overview.avgOpenRate}%</p>
            </div>
            <div className="flex items-center space-x-1 text-green-600">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm font-medium">+{data.overview.trends.openRate}%</span>
            </div>
          </div>
          <div className="mt-2">
            <Eye className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Click Rate</p>
              <p className="text-2xl font-bold text-gray-900">{data.overview.avgClickRate}%</p>
            </div>
            <div className="flex items-center space-x-1 text-green-600">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm font-medium">+{data.overview.trends.clickRate}%</span>
            </div>
          </div>
          <div className="mt-2">
            <MousePointer className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">${data.overview.totalRevenue.toLocaleString()}</p>
            </div>
            <div className="flex items-center space-x-1 text-green-600">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm font-medium">+{data.overview.trends.revenue}%</span>
            </div>
          </div>
          <div className="mt-2">
            <TrendingUp className="w-8 h-8 text-yellow-500" />
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Engagement Trends */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Engagement Trends</h3>
            <select
              value={selectedMetric}
              onChange={(e) => setSelectedMetric(e.target.value)}
              className="border border-gray-300 rounded px-2 py-1 text-sm"
            >
              <option value="openRate">Open Rate</option>
              <option value="clickRate">Click Rate</option>
              <option value="opens">Total Opens</option>
              <option value="clicks">Total Clicks</option>
            </select>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Area
                type="monotone"
                dataKey={selectedMetric}
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.1}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Template Performance */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Template Performance</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={templatePerformanceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="openRate" fill="#3B82F6" name="Open Rate %" />
              <Bar dataKey="clickRate" fill="#10B981" name="Click Rate %" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Performance Indicators</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {data.performance.map((metric, index) => (
            <div key={index} className="p-4 border rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">{metric.metric}</span>
                <div className={`flex items-center space-x-1 ${
                  metric.trend === 'up' ? 'text-green-600' : 
                  metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                }`}>
                  {metric.trend === 'up' ? <TrendingUp className="w-4 h-4" /> : 
                   metric.trend === 'down' ? <TrendingDown className="w-4 h-4" /> : null}
                  <span className="text-sm font-medium">
                    {metric.change > 0 ? '+' : ''}{metric.change}%
                  </span>
                </div>
              </div>
              <p className="text-xl font-bold text-gray-900 mt-1">
                {metric.value}%
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Campaigns */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Campaigns</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Campaign
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Open Rate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Click Rate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Revenue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.campaigns.map((campaign) => (
                <tr key={campaign.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {campaign.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {campaign.sent.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {((campaign.opened / campaign.sent) * 100).toFixed(1)}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {((campaign.clicked / campaign.sent) * 100).toFixed(1)}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${campaign.revenue.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(campaign.sentDate).toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default AnalyticsDashboard
