import { useEffect, useCallback, useRef } from 'react'

interface KeyboardShortcut {
  keys: string[]
  callback: (event: KeyboardEvent) => void
  preventDefault?: boolean
  stopPropagation?: boolean
  description?: string
  category?: string
  enabled?: boolean
}

interface UseKeyboardShortcutsOptions {
  enabled?: boolean
  preventDefault?: boolean
  stopPropagation?: boolean
}

// Normalize key combinations for cross-platform compatibility
const normalizeKey = (key: string): string => {
  const keyMap: Record<string, string> = {
    'cmd': 'meta',
    'command': 'meta',
    'ctrl': 'control',
    'ctl': 'control',
    'alt': 'alt',
    'option': 'alt',
    'shift': 'shift',
    'space': ' ',
    'spacebar': ' ',
    'esc': 'escape',
    'enter': 'enter',
    'return': 'enter',
    'tab': 'tab',
    'backspace': 'backspace',
    'delete': 'delete',
    'del': 'delete',
    'up': 'arrowup',
    'down': 'arrowdown',
    'left': 'arrowleft',
    'right': 'arrowright'
  }

  return keyMap[key.toLowerCase()] || key.toLowerCase()
}

// Parse key combination string (e.g., "ctrl+shift+s")
const parseKeyCombo = (combo: string): string[] => {
  return combo.toLowerCase().split('+').map(key => normalizeKey(key.trim()))
}

// Check if the pressed keys match the shortcut
const matchesShortcut = (event: KeyboardEvent, shortcutKeys: string[]): boolean => {
  const pressedKeys: string[] = []
  
  if (event.ctrlKey || event.metaKey) pressedKeys.push('control')
  if (event.altKey) pressedKeys.push('alt')
  if (event.shiftKey) pressedKeys.push('shift')
  
  const mainKey = normalizeKey(event.key)
  if (!['control', 'alt', 'shift', 'meta'].includes(mainKey)) {
    pressedKeys.push(mainKey)
  }

  // Sort both arrays for comparison
  const sortedPressed = pressedKeys.sort()
  const sortedShortcut = [...shortcutKeys].sort()

  return sortedPressed.length === sortedShortcut.length &&
         sortedPressed.every((key, index) => key === sortedShortcut[index])
}

// Main hook for keyboard shortcuts
export const useKeyboardShortcuts = (
  shortcuts: KeyboardShortcut[],
  options: UseKeyboardShortcutsOptions = {}
) => {
  const {
    enabled = true,
    preventDefault = true,
    stopPropagation = true
  } = options

  const shortcutsRef = useRef(shortcuts)
  shortcutsRef.current = shortcuts

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return

    // Skip if user is typing in an input field
    const target = event.target as HTMLElement
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
      return
    }

    for (const shortcut of shortcutsRef.current) {
      if (shortcut.enabled === false) continue

      for (const keyCombo of shortcut.keys) {
        const shortcutKeys = parseKeyCombo(keyCombo)
        
        if (matchesShortcut(event, shortcutKeys)) {
          if (shortcut.preventDefault ?? preventDefault) {
            event.preventDefault()
          }
          if (shortcut.stopPropagation ?? stopPropagation) {
            event.stopPropagation()
          }
          
          shortcut.callback(event)
          return // Only trigger the first matching shortcut
        }
      }
    }
  }, [enabled, preventDefault, stopPropagation])

  useEffect(() => {
    if (!enabled) return

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown, enabled])
}

// Hook for single shortcut
export const useKeyboardShortcut = (
  keys: string | string[],
  callback: (event: KeyboardEvent) => void,
  options: UseKeyboardShortcutsOptions & { description?: string } = {}
) => {
  const keyArray = Array.isArray(keys) ? keys : [keys]
  
  useKeyboardShortcuts([{
    keys: keyArray,
    callback,
    preventDefault: options.preventDefault,
    stopPropagation: options.stopPropagation,
    description: options.description
  }], options)
}

// Hook for managing global shortcuts with categories
export const useGlobalShortcuts = () => {
  const shortcuts = useRef<Map<string, KeyboardShortcut>>(new Map())

  const registerShortcut = useCallback((
    id: string,
    shortcut: KeyboardShortcut
  ) => {
    shortcuts.current.set(id, shortcut)
  }, [])

  const unregisterShortcut = useCallback((id: string) => {
    shortcuts.current.delete(id)
  }, [])

  const getShortcuts = useCallback(() => {
    return Array.from(shortcuts.current.values())
  }, [])

  const getShortcutsByCategory = useCallback((category: string) => {
    return Array.from(shortcuts.current.values())
      .filter(shortcut => shortcut.category === category)
  }, [])

  useKeyboardShortcuts(getShortcuts())

  return {
    registerShortcut,
    unregisterShortcut,
    getShortcuts,
    getShortcutsByCategory
  }
}

// Predefined shortcut sets
export const COMMON_SHORTCUTS = {
  SAVE: {
    keys: ['ctrl+s', 'cmd+s'],
    description: 'Save',
    category: 'File'
  },
  UNDO: {
    keys: ['ctrl+z', 'cmd+z'],
    description: 'Undo',
    category: 'Edit'
  },
  REDO: {
    keys: ['ctrl+y', 'cmd+shift+z', 'ctrl+shift+z'],
    description: 'Redo',
    category: 'Edit'
  },
  COPY: {
    keys: ['ctrl+c', 'cmd+c'],
    description: 'Copy',
    category: 'Edit'
  },
  PASTE: {
    keys: ['ctrl+v', 'cmd+v'],
    description: 'Paste',
    category: 'Edit'
  },
  CUT: {
    keys: ['ctrl+x', 'cmd+x'],
    description: 'Cut',
    category: 'Edit'
  },
  SELECT_ALL: {
    keys: ['ctrl+a', 'cmd+a'],
    description: 'Select All',
    category: 'Edit'
  },
  FIND: {
    keys: ['ctrl+f', 'cmd+f'],
    description: 'Find',
    category: 'Search'
  },
  NEW: {
    keys: ['ctrl+n', 'cmd+n'],
    description: 'New',
    category: 'File'
  },
  OPEN: {
    keys: ['ctrl+o', 'cmd+o'],
    description: 'Open',
    category: 'File'
  },
  CLOSE: {
    keys: ['ctrl+w', 'cmd+w'],
    description: 'Close',
    category: 'File'
  },
  REFRESH: {
    keys: ['f5', 'ctrl+r', 'cmd+r'],
    description: 'Refresh',
    category: 'View'
  },
  ZOOM_IN: {
    keys: ['ctrl+=', 'cmd+=', 'ctrl+plus'],
    description: 'Zoom In',
    category: 'View'
  },
  ZOOM_OUT: {
    keys: ['ctrl+-', 'cmd+-', 'ctrl+minus'],
    description: 'Zoom Out',
    category: 'View'
  },
  ZOOM_RESET: {
    keys: ['ctrl+0', 'cmd+0'],
    description: 'Reset Zoom',
    category: 'View'
  },
  ESCAPE: {
    keys: ['escape'],
    description: 'Cancel/Close',
    category: 'Navigation'
  },
  DELETE: {
    keys: ['delete', 'backspace'],
    description: 'Delete',
    category: 'Edit'
  }
}

// Template Editor specific shortcuts
export const TEMPLATE_EDITOR_SHORTCUTS = {
  PREVIEW: {
    keys: ['ctrl+p', 'cmd+p'],
    description: 'Preview Template',
    category: 'Template'
  },
  DUPLICATE: {
    keys: ['ctrl+d', 'cmd+d'],
    description: 'Duplicate Component',
    category: 'Template'
  },
  GROUP: {
    keys: ['ctrl+g', 'cmd+g'],
    description: 'Group Components',
    category: 'Template'
  },
  UNGROUP: {
    keys: ['ctrl+shift+g', 'cmd+shift+g'],
    description: 'Ungroup Components',
    category: 'Template'
  },
  BRING_FORWARD: {
    keys: ['ctrl+]', 'cmd+]'],
    description: 'Bring Forward',
    category: 'Template'
  },
  SEND_BACKWARD: {
    keys: ['ctrl+[', 'cmd+['],
    description: 'Send Backward',
    category: 'Template'
  },
  TOGGLE_GRID: {
    keys: ['ctrl+shift+g', 'cmd+shift+g'],
    description: 'Toggle Grid',
    category: 'View'
  },
  TOGGLE_RULERS: {
    keys: ['ctrl+shift+r', 'cmd+shift+r'],
    description: 'Toggle Rulers',
    category: 'View'
  }
}

// Utility function to format shortcut keys for display
export const formatShortcutKeys = (keys: string[]): string => {
  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0
  
  return keys.map(combo => {
    return combo.split('+').map(key => {
      switch (key.toLowerCase()) {
        case 'ctrl':
        case 'control':
          return isMac ? '⌃' : 'Ctrl'
        case 'cmd':
        case 'meta':
          return isMac ? '⌘' : 'Ctrl'
        case 'alt':
          return isMac ? '⌥' : 'Alt'
        case 'shift':
          return isMac ? '⇧' : 'Shift'
        case 'enter':
          return '↵'
        case 'escape':
          return 'Esc'
        case 'backspace':
          return '⌫'
        case 'delete':
          return isMac ? '⌦' : 'Del'
        case 'tab':
          return '⇥'
        case 'space':
          return '␣'
        case 'arrowup':
          return '↑'
        case 'arrowdown':
          return '↓'
        case 'arrowleft':
          return '←'
        case 'arrowright':
          return '→'
        default:
          return key.toUpperCase()
      }
    }).join(isMac ? '' : '+')
  }).join(' or ')
}
