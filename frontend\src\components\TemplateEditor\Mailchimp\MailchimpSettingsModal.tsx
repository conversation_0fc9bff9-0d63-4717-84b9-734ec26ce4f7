import React, { useState } from 'react'
import { X, Save, Palette, Type, Layout, Mail, Globe, Shield } from 'lucide-react'
import { TemplateSettings } from '../../../types/templateEditor'

interface MailchimpSettingsModalProps {
  isOpen: boolean
  onClose: () => void
  settings: TemplateSettings
  onSave: (settings: TemplateSettings) => void
}

export const MailchimpSettingsModal: React.FC<MailchimpSettingsModalProps> = ({
  isOpen,
  onClose,
  settings,
  onSave
}) => {
  const [localSettings, setLocalSettings] = useState<TemplateSettings>(settings)
  const [activeTab, setActiveTab] = useState<'design' | 'email' | 'advanced'>('design')

  if (!isOpen) return null

  const handleSave = () => {
    onSave(localSettings)
    onClose()
  }

  const updateSettings = (updates: Partial<TemplateSettings>) => {
    setLocalSettings(prev => ({ ...prev, ...updates }))
  }

  const tabs = [
    { id: 'design', name: 'Design', icon: Palette },
    { id: 'email', name: '<PERSON><PERSON> Settings', icon: Mail },
    { id: 'advanced', name: 'Advanced', icon: Shield }
  ]

  const renderDesignSettings = () => (
    <div className="space-y-6">
      {/* Template Dimensions */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Layout className="w-5 h-5 mr-2" />
          Template Layout
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Template Width (px)
            </label>
            <input
              type="number"
              value={localSettings.width || 600}
              onChange={(e) => updateSettings({ width: parseInt(e.target.value) })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              min="300"
              max="800"
            />
            <p className="text-xs text-gray-500 mt-1">Recommended: 600px for best compatibility</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Width (px)
            </label>
            <input
              type="number"
              value={localSettings.maxWidth || 600}
              onChange={(e) => updateSettings({ maxWidth: parseInt(e.target.value) })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              min="300"
              max="1000"
            />
          </div>
        </div>
      </div>

      {/* Colors */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Palette className="w-5 h-5 mr-2" />
          Color Scheme
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Background Color
            </label>
            <div className="flex items-center space-x-3">
              <input
                type="color"
                value={localSettings.backgroundColor || '#ffffff'}
                onChange={(e) => updateSettings({ backgroundColor: e.target.value })}
                className="w-12 h-10 border border-gray-300 rounded-md"
              />
              <input
                type="text"
                value={localSettings.backgroundColor || '#ffffff'}
                onChange={(e) => updateSettings({ backgroundColor: e.target.value })}
                className="flex-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="#ffffff"
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Primary Color
            </label>
            <div className="flex items-center space-x-3">
              <input
                type="color"
                value={localSettings.primaryColor || '#007C89'}
                onChange={(e) => updateSettings({ primaryColor: e.target.value })}
                className="w-12 h-10 border border-gray-300 rounded-md"
              />
              <input
                type="text"
                value={localSettings.primaryColor || '#007C89'}
                onChange={(e) => updateSettings({ primaryColor: e.target.value })}
                className="flex-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="#007C89"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Typography */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Type className="w-5 h-5 mr-2" />
          Typography
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Default Font Family
            </label>
            <select
              value={localSettings.fontFamily || 'Arial, sans-serif'}
              onChange={(e) => updateSettings({ fontFamily: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="Arial, sans-serif">Arial</option>
              <option value="Helvetica, sans-serif">Helvetica</option>
              <option value="Georgia, serif">Georgia</option>
              <option value="Times New Roman, serif">Times New Roman</option>
              <option value="Courier New, monospace">Courier New</option>
              <option value="Verdana, sans-serif">Verdana</option>
              <option value="Tahoma, sans-serif">Tahoma</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Base Font Size (px)
            </label>
            <input
              type="number"
              value={localSettings.fontSize || 16}
              onChange={(e) => updateSettings({ fontSize: parseInt(e.target.value) })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              min="12"
              max="24"
            />
          </div>
        </div>
      </div>

      {/* Spacing */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Default Spacing</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Content Padding (px)
            </label>
            <input
              type="number"
              value={localSettings.contentPadding || 20}
              onChange={(e) => updateSettings({ contentPadding: parseInt(e.target.value) })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              min="0"
              max="50"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Section Spacing (px)
            </label>
            <input
              type="number"
              value={localSettings.sectionSpacing || 30}
              onChange={(e) => updateSettings({ sectionSpacing: parseInt(e.target.value) })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              min="0"
              max="100"
            />
          </div>
        </div>
      </div>
    </div>
  )

  const renderEmailSettings = () => (
    <div className="space-y-6">
      {/* Email Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Mail className="w-5 h-5 mr-2" />
          Email Information
        </h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              From Name
            </label>
            <input
              type="text"
              value={localSettings.fromName || ''}
              onChange={(e) => updateSettings({ fromName: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Your Company Name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              From Email
            </label>
            <input
              type="email"
              value={localSettings.fromEmail || ''}
              onChange={(e) => updateSettings({ fromEmail: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Reply-To Email
            </label>
            <input
              type="email"
              value={localSettings.replyTo || ''}
              onChange={(e) => updateSettings({ replyTo: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="<EMAIL>"
            />
          </div>
        </div>
      </div>

      {/* Company Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Globe className="w-5 h-5 mr-2" />
          Company Information
        </h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Company Name
            </label>
            <input
              type="text"
              value={localSettings.companyName || ''}
              onChange={(e) => updateSettings({ companyName: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Your Company Name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Company Address
            </label>
            <textarea
              value={localSettings.companyAddress || ''}
              onChange={(e) => updateSettings({ companyAddress: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={3}
              placeholder="123 Business St, City, State 12345"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Website URL
            </label>
            <input
              type="url"
              value={localSettings.websiteUrl || ''}
              onChange={(e) => updateSettings({ websiteUrl: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="https://yourcompany.com"
            />
          </div>
        </div>
      </div>

      {/* Social Media */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Social Media Links</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Facebook
            </label>
            <input
              type="url"
              value={localSettings.socialMedia?.facebook || ''}
              onChange={(e) => updateSettings({ 
                socialMedia: { ...localSettings.socialMedia, facebook: e.target.value }
              })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="https://facebook.com/yourcompany"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Twitter
            </label>
            <input
              type="url"
              value={localSettings.socialMedia?.twitter || ''}
              onChange={(e) => updateSettings({ 
                socialMedia: { ...localSettings.socialMedia, twitter: e.target.value }
              })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="https://twitter.com/yourcompany"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Instagram
            </label>
            <input
              type="url"
              value={localSettings.socialMedia?.instagram || ''}
              onChange={(e) => updateSettings({ 
                socialMedia: { ...localSettings.socialMedia, instagram: e.target.value }
              })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="https://instagram.com/yourcompany"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              LinkedIn
            </label>
            <input
              type="url"
              value={localSettings.socialMedia?.linkedin || ''}
              onChange={(e) => updateSettings({ 
                socialMedia: { ...localSettings.socialMedia, linkedin: e.target.value }
              })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="https://linkedin.com/company/yourcompany"
            />
          </div>
        </div>
      </div>
    </div>
  )

  const renderAdvancedSettings = () => (
    <div className="space-y-6">
      {/* Email Client Compatibility */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Shield className="w-5 h-5 mr-2" />
          Email Client Compatibility
        </h3>
        <div className="space-y-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={localSettings.darkModeSupport || false}
              onChange={(e) => updateSettings({ darkModeSupport: e.target.checked })}
              className="mr-3"
            />
            <label className="text-sm text-gray-700">
              Enable dark mode support
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={localSettings.outlookCompatibility || true}
              onChange={(e) => updateSettings({ outlookCompatibility: e.target.checked })}
              className="mr-3"
            />
            <label className="text-sm text-gray-700">
              Optimize for Outlook compatibility
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={localSettings.mobileOptimized || true}
              onChange={(e) => updateSettings({ mobileOptimized: e.target.checked })}
              className="mr-3"
            />
            <label className="text-sm text-gray-700">
              Mobile-optimized rendering
            </label>
          </div>
        </div>
      </div>

      {/* Custom CSS */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Custom CSS</h3>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Additional CSS (Advanced users only)
          </label>
          <textarea
            value={localSettings.customCSS || ''}
            onChange={(e) => updateSettings({ customCSS: e.target.value })}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
            rows={6}
            placeholder="/* Add your custom CSS here */"
          />
          <p className="text-xs text-gray-500 mt-1">
            Custom CSS will be injected into the email template. Use with caution as it may affect email client compatibility.
          </p>
        </div>
      </div>

      {/* Tracking */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Tracking & Analytics</h3>
        <div className="space-y-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={localSettings.trackOpens || true}
              onChange={(e) => updateSettings({ trackOpens: e.target.checked })}
              className="mr-3"
            />
            <label className="text-sm text-gray-700">
              Track email opens
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={localSettings.trackClicks || true}
              onChange={(e) => updateSettings({ trackClicks: e.target.checked })}
              className="mr-3"
            />
            <label className="text-sm text-gray-700">
              Track link clicks
            </label>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Google Analytics UTM Campaign
            </label>
            <input
              type="text"
              value={localSettings.utmCampaign || ''}
              onChange={(e) => updateSettings({ utmCampaign: e.target.value })}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="email_campaign_name"
            />
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">Template Settings</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map(tab => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4 inline mr-2" />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {activeTab === 'design' && renderDesignSettings()}
          {activeTab === 'email' && renderEmailSettings()}
          {activeTab === 'advanced' && renderAdvancedSettings()}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-100 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Save className="w-4 h-4 mr-2" />
              Save Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MailchimpSettingsModal
