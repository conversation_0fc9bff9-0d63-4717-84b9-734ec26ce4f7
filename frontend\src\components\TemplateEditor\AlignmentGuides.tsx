import React from 'react'

interface AlignmentGuidesProps {
  type: 'vertical' | 'horizontal'
  position: number
}

export const AlignmentGuides: React.FC<AlignmentGuidesProps> = ({ type, position }) => {
  return (
    <div
      className={`absolute pointer-events-none z-40 ${
        type === 'vertical'
          ? 'w-px h-full bg-green-400 opacity-60'
          : 'h-px w-full bg-green-400 opacity-60'
      }`}
      style={{
        [type === 'vertical' ? 'left' : 'top']: `${position}px`
      }}
    />
  )
}
