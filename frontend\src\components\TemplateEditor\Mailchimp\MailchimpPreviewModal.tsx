import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { X, Smartphone, Monitor, Tablet, Send, Download, Share2, AlertCircle, Loader2 } from 'lucide-react'
import { TemplateComponent, TemplateSettings } from '../../../types/templateEditor'
import { MailchimpComponentRenderer } from './MailchimpComponentRenderer'

interface MailchimpPreviewModalProps {
  isOpen: boolean
  onClose: () => void
  components: TemplateComponent[]
  templateSettings: TemplateSettings
  templateName: string
  onSendTest?: () => void
  onExport?: () => void
  onShare?: () => void
  loading?: boolean
  error?: string | null
}

// Error Boundary Component
class PreviewErrorBoundary extends React.Component<
  { children: React.ReactNode; onError?: (error: Error) => void },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Preview Error:', error, errorInfo)
    this.props.onError?.(error)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center h-96 text-gray-500 p-8">
          <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
          <h3 className="text-lg font-medium mb-2 text-red-700">Preview Error</h3>
          <p className="text-sm text-center max-w-sm text-gray-600">
            There was an error rendering the preview. Please check your template components.
          </p>
          <button
            onClick={() => this.setState({ hasError: false, error: undefined })}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      )
    }

    return this.props.children
  }
}

export const MailchimpPreviewModal: React.FC<MailchimpPreviewModalProps> = ({
  isOpen,
  onClose,
  components,
  templateSettings,
  templateName,
  onSendTest,
  onExport,
  onShare,
  loading = false,
  error = null
}) => {
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [isRendering, setIsRendering] = useState(false)

  // Memoize dimensions calculation
  const dimensions = useMemo(() => {
    switch (previewMode) {
      case 'mobile':
        return { width: 375, height: 667, scale: 0.8 }
      case 'tablet':
        return { width: 768, height: 1024, scale: 0.9 }
      case 'desktop':
      default:
        return { width: 600, height: 800, scale: 1 }
    }
  }, [previewMode])

  // Memoize preview styles
  const previewStyles = useMemo((): React.CSSProperties => ({
    width: dimensions.width,
    minHeight: dimensions.height,
    backgroundColor: templateSettings?.backgroundColor || '#ffffff',
    fontFamily: templateSettings?.fontFamily || 'Arial, sans-serif',
    margin: '0 auto',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    transform: `scale(${dimensions.scale})`,
    transformOrigin: 'top center',
    transition: 'all 0.3s ease-in-out'
  }), [dimensions, templateSettings])

  // Handle component rendering with error handling
  const handleComponentRender = useCallback(() => {
    if (!components || components.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-96 text-gray-500">
          <div className="text-6xl mb-4">📧</div>
          <h3 className="text-lg font-medium mb-2">No content to preview</h3>
          <p className="text-sm text-center max-w-sm">
            Add some content blocks to your template to see the preview.
          </p>
        </div>
      )
    }

    return components.map((component, index) => {
      try {
        return (
          <MailchimpComponentRenderer
            key={component.id || `component-${index}`}
            component={component}
            isSelected={false}
            onUpdate={() => {}}
            onDelete={() => {}}
            templateSettings={templateSettings}
          />
        )
      } catch (error) {
        console.error('Error rendering component:', component, error)
        return (
          <div key={component.id || `error-${index}`} className="p-4 bg-red-50 border border-red-200 rounded-lg m-2">
            <div className="flex items-center text-red-700">
              <AlertCircle className="w-4 h-4 mr-2" />
              <span className="text-sm">Error rendering component: {component.type}</span>
            </div>
          </div>
        )
      }
    })
  }, [components, templateSettings])

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return
      
      if (event.key === 'Escape') {
        onClose()
      } else if (event.key === '1' && event.ctrlKey) {
        setPreviewMode('desktop')
      } else if (event.key === '2' && event.ctrlKey) {
        setPreviewMode('tablet')
      } else if (event.key === '3' && event.ctrlKey) {
        setPreviewMode('mobile')
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, onClose])

  // Simulate rendering delay for better UX
  useEffect(() => {
    if (isOpen) {
      setIsRendering(true)
      const timer = setTimeout(() => setIsRendering(false), 300)
      return () => clearTimeout(timer)
    }
  }, [isOpen, previewMode])

  if (!isOpen) return null

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      role="dialog"
      aria-modal="true"
      aria-labelledby="preview-modal-title"
    >
      <div className="bg-white rounded-lg shadow-xl w-full max-w-7xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 id="preview-modal-title" className="text-xl font-bold text-gray-900">
              Preview: {templateName || 'Untitled Template'}
            </h2>
            <p className="text-gray-600 text-sm mt-1">
              See how your email will look to recipients
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Preview Mode Selector */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1" role="tablist">
              <button
                onClick={() => setPreviewMode('desktop')}
                className={`p-2 rounded-md transition-colors ${
                  previewMode === 'desktop'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Desktop View (Ctrl+1)"
                role="tab"
                aria-selected={previewMode === 'desktop'}
              >
                <Monitor className="w-4 h-4" />
              </button>
              <button
                onClick={() => setPreviewMode('tablet')}
                className={`p-2 rounded-md transition-colors ${
                  previewMode === 'tablet'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Tablet View (Ctrl+2)"
                role="tab"
                aria-selected={previewMode === 'tablet'}
              >
                <Tablet className="w-4 h-4" />
              </button>
              <button
                onClick={() => setPreviewMode('mobile')}
                className={`p-2 rounded-md transition-colors ${
                  previewMode === 'mobile'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Mobile View (Ctrl+3)"
                role="tab"
                aria-selected={previewMode === 'mobile'}
              >
                <Smartphone className="w-4 h-4" />
              </button>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2">
              {onSendTest && (
                <button
                  onClick={onSendTest}
                  disabled={loading}
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Send className="w-4 h-4 mr-2" />
                  Send Test
                </button>
              )}
              
              {onExport && (
                <button
                  onClick={onExport}
                  disabled={loading}
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </button>
              )}
              
              {onShare && (
                <button
                  onClick={onShare}
                  disabled={loading}
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </button>
              )}
            </div>

            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              title="Close Preview (Esc)"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Preview Content */}
        <div className="flex-1 overflow-auto bg-gray-100 p-8">
          {error ? (
            <div className="flex flex-col items-center justify-center h-full text-red-600">
              <AlertCircle className="w-12 h-12 mb-4" />
              <h3 className="text-lg font-medium mb-2">Preview Error</h3>
              <p className="text-sm text-center max-w-sm">{error}</p>
            </div>
          ) : (
            <div className="flex justify-center">
              {(loading || isRendering) && (
                <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                  <div className="flex items-center space-x-2">
                    <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
                    <span className="text-gray-600">Loading preview...</span>
                  </div>
                </div>
              )}
              
              <div
                className="bg-white rounded-lg overflow-hidden shadow-lg"
                style={previewStyles}
              >
                {/* Email Header Simulation */}
                <div className="bg-gray-50 border-b border-gray-200 p-4 text-sm text-gray-600">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">From: <EMAIL></div>
                      <div>To: <EMAIL></div>
                      <div className="font-medium mt-1">Subject: {templateName || 'Your Email Subject'}</div>
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date().toLocaleDateString()}
                    </div>
                  </div>
                </div>

                {/* Template Content */}
                <div className="relative">
                  <PreviewErrorBoundary>
                    {handleComponentRender()}
                  </PreviewErrorBoundary>
                </div>

                {/* Email Footer Simulation */}
                <div className="bg-gray-50 border-t border-gray-200 p-4 text-xs text-gray-500 text-center">
                  <div className="mb-2">
                    This email was <NAME_EMAIL>
                  </div>
                  <div className="flex items-center justify-center space-x-4 flex-wrap">
                    <a href="#" className="text-blue-600 hover:underline">Unsubscribe</a>
                    <span>•</span>
                    <a href="#" className="text-blue-600 hover:underline">Update Preferences</a>
                    <span>•</span>
                    <a href="#" className="text-blue-600 hover:underline">View in Browser</a>
                  </div>
                  <div className="mt-2">
                    Your Company<br />
                    Your Company Address
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Preview shows how your email will appear in most email clients.
              Actual rendering may vary slightly between different email providers.
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">
                {dimensions.width} × {dimensions.height}px
              </span>
              <span className="text-sm text-gray-500 capitalize">
                ({previewMode})
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MailchimpPreviewModal
