import React, { useState } from 'react'
import { X, Smartphone, Monitor, Tablet, Send, Download, Share2 } from 'lucide-react'
import { TemplateComponent, TemplateSettings } from '../../../types/templateEditor'
import { MailchimpComponent<PERSON>enderer } from './MailchimpComponentRenderer'

interface MailchimpPreviewModalProps {
  isOpen: boolean
  onClose: () => void
  components: TemplateComponent[]
  templateSettings: TemplateSettings
  templateName: string
  onSendTest?: () => void
  onExport?: () => void
  onShare?: () => void
}

export const MailchimpPreviewModal: React.FC<MailchimpPreviewModalProps> = ({
  isOpen,
  onClose,
  components,
  templateSettings,
  templateName,
  onSendTest,
  onExport,
  onShare
}) => {
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')

  if (!isOpen) return null

  const getPreviewDimensions = () => {
    switch (previewMode) {
      case 'mobile':
        return { width: 375, height: 667 }
      case 'tablet':
        return { width: 768, height: 1024 }
      case 'desktop':
      default:
        return { width: 600, height: 800 }
    }
  }

  const dimensions = getPreviewDimensions()

  const previewStyles: React.CSSProperties = {
    width: dimensions.width,
    minHeight: dimensions.height,
    backgroundColor: templateSettings.backgroundColor || '#ffffff',
    fontFamily: templateSettings.fontFamily || 'Arial, sans-serif',
    margin: '0 auto',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-7xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Preview: {templateName}</h2>
            <p className="text-gray-600 text-sm mt-1">See how your email will look to recipients</p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Preview Mode Selector */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setPreviewMode('desktop')}
                className={`p-2 rounded-md transition-colors ${
                  previewMode === 'desktop'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Desktop View"
              >
                <Monitor className="w-4 h-4" />
              </button>
              <button
                onClick={() => setPreviewMode('tablet')}
                className={`p-2 rounded-md transition-colors ${
                  previewMode === 'tablet'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Tablet View"
              >
                <Tablet className="w-4 h-4" />
              </button>
              <button
                onClick={() => setPreviewMode('mobile')}
                className={`p-2 rounded-md transition-colors ${
                  previewMode === 'mobile'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Mobile View"
              >
                <Smartphone className="w-4 h-4" />
              </button>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2">
              {onSendTest && (
                <button
                  onClick={onSendTest}
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  <Send className="w-4 h-4 mr-2" />
                  Send Test
                </button>
              )}
              
              {onExport && (
                <button
                  onClick={onExport}
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </button>
              )}
              
              {onShare && (
                <button
                  onClick={onShare}
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </button>
              )}
            </div>

            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Preview Content */}
        <div className="flex-1 overflow-auto bg-gray-100 p-8">
          <div className="flex justify-center">
            <div
              className="bg-white rounded-lg overflow-hidden"
              style={previewStyles}
            >
              {/* Email Header Simulation */}
              <div className="bg-gray-50 border-b border-gray-200 p-4 text-sm text-gray-600">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">From: <EMAIL></div>
                    <div>To: <EMAIL></div>
                    <div className="font-medium mt-1">Subject: {templateName}</div>
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date().toLocaleDateString()}
                  </div>
                </div>
              </div>

              {/* Template Content */}
              <div className="p-0">
                {components.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-96 text-gray-500">
                    <div className="text-6xl mb-4">📧</div>
                    <h3 className="text-lg font-medium mb-2">No content to preview</h3>
                    <p className="text-sm text-center max-w-sm">
                      Add some content blocks to your template to see the preview.
                    </p>
                  </div>
                ) : (
                  components.map(component => (
                    <MailchimpComponentRenderer
                      key={component.id}
                      component={component}
                      isSelected={false}
                      onUpdate={() => {}}
                      onDelete={() => {}}
                      templateSettings={templateSettings}
                    />
                  ))
                )}
              </div>

              {/* Email Footer Simulation */}
              <div className="bg-gray-50 border-t border-gray-200 p-4 text-xs text-gray-500 text-center">
                <div className="mb-2">
                  This email was <NAME_EMAIL>
                </div>
                <div className="flex items-center justify-center space-x-4">
                  <a href="#" className="text-blue-600 hover:underline">Unsubscribe</a>
                  <span>•</span>
                  <a href="#" className="text-blue-600 hover:underline">Update Preferences</a>
                  <span>•</span>
                  <a href="#" className="text-blue-600 hover:underline">View in Browser</a>
                </div>
                <div className="mt-2">
                  Your Company Name<br />
                  123 Business St, City, State 12345
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Preview shows how your email will appear in most email clients.
              Actual rendering may vary slightly between different email providers.
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">
                {dimensions.width} × {dimensions.height}px
              </span>
              <span className="text-sm text-gray-500 capitalize">
                ({previewMode})
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MailchimpPreviewModal
