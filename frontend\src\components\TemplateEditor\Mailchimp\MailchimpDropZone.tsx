import React from 'react'
import { useDrop } from 'react-dnd'
import { TemplateComponent } from '../../../types/templateEditor'
import { Plus } from 'lucide-react'

interface MailchimpDropZoneProps {
  index: number
  isActive: boolean
  onDrop: (component: TemplateComponent) => void
  onDragOver: () => void
  onDragLeave: () => void
}

export const MailchimpDropZone: React.FC<MailchimpDropZoneProps> = ({
  index,
  isActive,
  onDrop,
  onDragOver,
  onDragLeave
}) => {
  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: 'component',
    drop: (item: { component: any }, monitor) => {
      if (monitor.didDrop()) return

      const newComponent: TemplateComponent = {
        id: `component-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type: item.component.componentType,
        content: { ...item.component.defaultContent },
        styles: { ...item.component.defaultStyles },
        position: { x: 0, y: 0 }
      }
      
      onDrop(newComponent)
    },
    hover: () => {
      onDragOver()
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
  }))

  const handleMouseLeave = () => {
    if (!isOver) {
      onDragLeave()
    }
  }

  return (
    <div
      ref={drop}
      className={`relative transition-all duration-200 ease-in-out ${
        isActive || isOver
          ? 'h-16 opacity-100'
          : 'h-2 opacity-0 hover:opacity-50'
      }`}
      onMouseLeave={handleMouseLeave}
    >
      <div
        className={`absolute inset-0 transition-all duration-200 ${
          isActive || isOver
            ? 'bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg'
            : 'bg-gray-100 border border-dashed border-gray-300 rounded'
        }`}
      >
        {(isActive || isOver) && (
          <div className="flex items-center justify-center h-full">
            <div className="flex items-center space-x-2 text-blue-600">
              <Plus className="w-4 h-4" />
              <span className="text-sm font-medium">Drop content block here</span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default MailchimpDropZone
