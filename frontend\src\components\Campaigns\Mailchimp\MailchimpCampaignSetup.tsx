import React, { useState } from 'react'
import { Info, Target, Zap, BarChart3, Mail, HelpCircle } from 'lucide-react'

interface CampaignData {
  name: string
  subject: string
  preheader: string
  fromName: string
  fromEmail: string
  replyTo: string
  type: 'regular' | 'ab_test' | 'automated' | 'rss'
  abTest?: {
    testType: 'subject' | 'from_name' | 'content'
    testPercentage: number
    winnerCriteria: 'opens' | 'clicks'
    testDuration: number
  }
  tracking: {
    opens: boolean
    clicks: boolean
    googleAnalytics: boolean
    facebookPixel: boolean
  }
}

interface MailchimpCampaignSetupProps {
  campaignData: CampaignData
  onUpdate: (updates: Partial<CampaignData>) => void
  onNext: () => void
  onPrevious: () => void
  canProceed: boolean
  isLastStep: boolean
  loading: boolean
}

export const MailchimpCampaignSetup: React.FC<MailchimpCampaignSetupProps> = ({
  campaignData,
  onUpdate,
  onNext,
  canProceed,
  loading
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false)

  const campaignTypes = [
    {
      id: 'regular',
      name: 'Regular Campaign',
      description: 'Send a one-time email to your audience',
      icon: Mail,
      color: 'blue'
    },
    {
      id: 'ab_test',
      name: 'A/B Test Campaign',
      description: 'Test different versions to optimize performance',
      icon: Target,
      color: 'green'
    },
    {
      id: 'automated',
      name: 'Automated Campaign',
      description: 'Set up triggered emails based on user behavior',
      icon: Zap,
      color: 'yellow'
    },
    {
      id: 'rss',
      name: 'RSS Campaign',
      description: 'Automatically send emails when new content is published',
      icon: BarChart3,
      color: 'purple'
    }
  ]

  const getTypeColor = (color: string) => {
    const colors = {
      blue: 'border-blue-200 bg-blue-50 text-blue-700',
      green: 'border-green-200 bg-green-50 text-green-700',
      yellow: 'border-yellow-200 bg-yellow-50 text-yellow-700',
      purple: 'border-purple-200 bg-purple-50 text-purple-700'
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  const updateField = (field: keyof CampaignData, value: any) => {
    onUpdate({ [field]: value })
  }

  const updateABTest = (field: string, value: any) => {
    onUpdate({
      abTest: {
        ...campaignData.abTest,
        [field]: value
      } as any
    })
  }

  const updateTracking = (field: string, value: boolean) => {
    onUpdate({
      tracking: {
        ...campaignData.tracking,
        [field]: value
      }
    })
  }

  return (
    <div className="space-y-8">
      {/* Campaign Type Selection */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Campaign Type</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {campaignTypes.map((type) => {
            const Icon = type.icon
            const isSelected = campaignData.type === type.id
            
            return (
              <button
                key={type.id}
                onClick={() => updateField('type', type.id)}
                className={`p-4 border-2 rounded-lg text-left transition-all ${
                  isSelected
                    ? `${getTypeColor(type.color)} border-opacity-100`
                    : 'border-gray-200 hover:border-gray-300 bg-white'
                }`}
              >
                <div className="flex items-start space-x-3">
                  <Icon className={`w-6 h-6 mt-1 ${
                    isSelected ? '' : 'text-gray-400'
                  }`} />
                  <div>
                    <h3 className={`font-medium ${
                      isSelected ? '' : 'text-gray-900'
                    }`}>
                      {type.name}
                    </h3>
                    <p className={`text-sm mt-1 ${
                      isSelected ? 'opacity-80' : 'text-gray-600'
                    }`}>
                      {type.description}
                    </p>
                  </div>
                </div>
              </button>
            )
          })}
        </div>
      </div>

      {/* A/B Test Settings */}
      {campaignData.type === 'ab_test' && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-green-900 mb-4">A/B Test Settings</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-green-800 mb-2">
                What to test
              </label>
              <select
                value={campaignData.abTest?.testType || 'subject'}
                onChange={(e) => updateABTest('testType', e.target.value)}
                className="w-full p-3 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="subject">Subject Line</option>
                <option value="from_name">From Name</option>
                <option value="content">Email Content</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-green-800 mb-2">
                Test percentage
              </label>
              <select
                value={campaignData.abTest?.testPercentage || 10}
                onChange={(e) => updateABTest('testPercentage', parseInt(e.target.value))}
                className="w-full p-3 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value={10}>10%</option>
                <option value={15}>15%</option>
                <option value={25}>25%</option>
                <option value={50}>50%</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-green-800 mb-2">
                Winner criteria
              </label>
              <select
                value={campaignData.abTest?.winnerCriteria || 'opens'}
                onChange={(e) => updateABTest('winnerCriteria', e.target.value)}
                className="w-full p-3 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="opens">Highest open rate</option>
                <option value="clicks">Highest click rate</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-green-800 mb-2">
                Test duration (hours)
              </label>
              <select
                value={campaignData.abTest?.testDuration || 4}
                onChange={(e) => updateABTest('testDuration', parseInt(e.target.value))}
                className="w-full p-3 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value={1}>1 hour</option>
                <option value={4}>4 hours</option>
                <option value={8}>8 hours</option>
                <option value={24}>24 hours</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Basic Campaign Details */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Campaign Details</h2>
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Campaign Name *
            </label>
            <input
              type="text"
              value={campaignData.name}
              onChange={(e) => updateField('name', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter campaign name"
            />
            <p className="text-xs text-gray-500 mt-1">
              This is for your reference and won't be seen by recipients
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Subject *
              </label>
              <input
                type="text"
                value={campaignData.subject}
                onChange={(e) => updateField('subject', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter email subject"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preview Text
                <HelpCircle className="w-4 h-4 inline ml-1 text-gray-400" />
              </label>
              <input
                type="text"
                value={campaignData.preheader}
                onChange={(e) => updateField('preheader', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Optional preview text"
              />
              <p className="text-xs text-gray-500 mt-1">
                Appears after the subject line in email clients
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* From Information */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-4">From Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              From Name *
            </label>
            <input
              type="text"
              value={campaignData.fromName}
              onChange={(e) => updateField('fromName', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Your name or company"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              From Email *
            </label>
            <input
              type="email"
              value={campaignData.fromEmail}
              onChange={(e) => updateField('fromEmail', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="<EMAIL>"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Reply-To Email
            </label>
            <input
              type="email"
              value={campaignData.replyTo}
              onChange={(e) => updateField('replyTo', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Leave blank to use From Email"
            />
          </div>
        </div>
      </div>

      {/* Tracking Settings */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Tracking & Analytics</h2>
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            {showAdvanced ? 'Hide' : 'Show'} Advanced Options
          </button>
        </div>

        <div className="space-y-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="track-opens"
              checked={campaignData.tracking.opens}
              onChange={(e) => updateTracking('opens', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="track-opens" className="ml-3 text-sm text-gray-700">
              Track opens
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="track-clicks"
              checked={campaignData.tracking.clicks}
              onChange={(e) => updateTracking('clicks', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="track-clicks" className="ml-3 text-sm text-gray-700">
              Track clicks
            </label>
          </div>

          {showAdvanced && (
            <>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="google-analytics"
                  checked={campaignData.tracking.googleAnalytics}
                  onChange={(e) => updateTracking('googleAnalytics', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="google-analytics" className="ml-3 text-sm text-gray-700">
                  Google Analytics tracking
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="facebook-pixel"
                  checked={campaignData.tracking.facebookPixel}
                  onChange={(e) => updateTracking('facebookPixel', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="facebook-pixel" className="ml-3 text-sm text-gray-700">
                  Facebook Pixel tracking
                </label>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Validation Messages */}
      {!canProceed && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <Info className="w-5 h-5 text-yellow-400 mr-3 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800">
                Please complete the required fields
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-disc list-inside space-y-1">
                  {!campaignData.name && <li>Campaign name is required</li>}
                  {!campaignData.subject && <li>Email subject is required</li>}
                  {!campaignData.fromName && <li>From name is required</li>}
                  {!campaignData.fromEmail && <li>From email is required</li>}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default MailchimpCampaignSetup
