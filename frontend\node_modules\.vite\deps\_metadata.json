{"hash": "a4a3a7cb", "configHash": "6647a2a6", "lockfileHash": "6facd633", "browserHash": "301708fc", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "3388a1e1", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "6edb8032", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "532ac8ba", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "ba58207e", "needsInterop": true}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "55e8805c", "needsInterop": false}, "lodash": {"src": "../../lodash/lodash.js", "file": "lodash.js", "fileHash": "348d2b8e", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "08af78ea", "needsInterop": false}, "react-dnd": {"src": "../../react-dnd/dist/index.js", "file": "react-dnd.js", "fileHash": "73e6e8d5", "needsInterop": false}, "react-dnd-html5-backend": {"src": "../../react-dnd-html5-backend/dist/index.js", "file": "react-dnd-html5-backend.js", "fileHash": "9bbdb1a0", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "68f704a5", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "d2d5328f", "needsInterop": false}, "react-toastify": {"src": "../../react-toastify/dist/index.mjs", "file": "react-toastify.js", "fileHash": "270e411b", "needsInterop": false}, "uuid": {"src": "../../uuid/dist/esm-browser/index.js", "file": "uuid.js", "fileHash": "7f44d14b", "needsInterop": false}}, "chunks": {"chunk-MJNCUEZK": {"file": "chunk-MJNCUEZK.js"}, "chunk-HE4GKDYE": {"file": "chunk-HE4GKDYE.js"}, "chunk-UGC3UZ7L": {"file": "chunk-UGC3UZ7L.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}