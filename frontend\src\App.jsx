import { Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from './contexts/AuthContext'
import { ProtectedRoute } from './components/ProtectedRoute'
import NotificationContainer from './components/NotificationContainer'
import { useNotifications } from './hooks/useNotifications'
import Login from './pages/Login'
import Dashboard from './pages/Dashboard'
import Templates from './pages/Templates'
import TemplateEditor from './pages/TemplateEditor'
import Campaigns from './pages/Campaigns'
import CampaignCreator from './pages/CampaignCreator'
import Contacts from './pages/Contacts'
import Analytics from './pages/Analytics'
import Profile from './pages/Profile'
import Layout from './components/Layout'

function App() {
  const { notifications, removeNotification } = useNotifications()

  return (
    <AuthProvider>
      <div className="min-h-screen bg-gray-50">
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <Layout>
                  <Routes>
                    <Route path="/" element={<Navigate to="/dashboard" replace />} />
                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route path="/templates" element={<Templates />} />
                    <Route path="/templates/new" element={<TemplateEditor />} />
                    <Route path="/templates/edit/:id" element={<TemplateEditor />} />
                    <Route path="/campaigns" element={<Campaigns />} />
                    <Route path="/campaigns/new" element={<CampaignCreator />} />
                    <Route path="/campaigns/edit/:id" element={<CampaignCreator />} />
                    <Route path="/contacts" element={<Contacts />} />
                    <Route path="/analytics" element={<Analytics />} />
                    <Route path="/profile" element={<Profile />} />
                  </Routes>
                </Layout>
              </ProtectedRoute>
            }
          />
        </Routes>
        <NotificationContainer
          notifications={notifications}
          onClose={removeNotification}
        />
      </div>
    </AuthProvider>
  )
}

export default App
