import React from 'react'
import { TemplateComponent, TemplateSettings } from '../../../types/templateEditor'

interface MailchimpComponentRendererProps {
  component: TemplateComponent
  isSelected: boolean
  onUpdate: (updates: Partial<TemplateComponent>) => void
  onDelete: () => void
  templateSettings: TemplateSettings
}

export const MailchimpComponentRenderer: React.FC<MailchimpComponentRendererProps> = ({
  component,
  isSelected,
  onUpdate,
  onDelete,
  templateSettings
}) => {
  // Safely handle component data
  const safeComponent = {
    ...component,
    content: component.content || {},
    styles: component.styles || {},
    type: component.type || 'text'
  }

  const renderComponent = () => {
    const baseStyles = {
      ...safeComponent.styles,
      outline: isSelected ? '2px solid #3B82F6' : 'none',
      outlineOffset: isSelected ? '2px' : '0'
    }

    // Handle unknown or unsupported component types
    if (!safeComponent.type) {
      return (
        <div
          style={{
            ...baseStyles,
            padding: '20px',
            border: '2px dashed #ccc',
            textAlign: 'center',
            color: '#666'
          }}
        >
          Missing component type
        </div>
      )
    }

    switch (safeComponent.type) {
      case 'text':
        return (
          <div
            style={baseStyles}
            className="mailchimp-text-component"
            dangerouslySetInnerHTML={{ __html: safeComponent.content.text || 'Your text here...' }}
          />
        )

      case 'heading':
        const HeadingTag = safeComponent.content.level || 'h2'
        return React.createElement(
          HeadingTag,
          {
            style: baseStyles,
            className: 'mailchimp-heading-component'
          },
          safeComponent.content.text || 'Your Heading'
        )

      case 'image':
        return (
          <div style={{ ...baseStyles, textAlign: 'center' }}>
            {safeComponent.content.link ? (
              <a href={safeComponent.content.link} target="_blank" rel="noopener noreferrer">
                <img
                  src={safeComponent.content.src || 'https://via.placeholder.com/600x300/f0f0f0/666666?text=Image'}
                  alt={safeComponent.content.alt || 'Image'}
                  style={{
                    maxWidth: '100%',
                    height: 'auto',
                    display: 'block',
                    margin: '0 auto'
                  }}
                  className="mailchimp-image-component"
                />
              </a>
            ) : (
              <img
                src={safeComponent.content.src || 'https://via.placeholder.com/600x300/f0f0f0/666666?text=Image'}
                alt={safeComponent.content.alt || 'Image'}
                style={{
                  maxWidth: '100%',
                  height: 'auto',
                  display: 'block',
                  margin: '0 auto'
                }}
                className="mailchimp-image-component"
              />
            )}
          </div>
        )

      case 'button':
        return (
          <div style={{ textAlign: 'center', padding: '16px' }}>
            <a
              href={safeComponent.content.href || '#'}
              target={safeComponent.content.target || '_blank'}
              style={baseStyles}
              className="mailchimp-button-component"
            >
              {safeComponent.content.text || 'Click Here'}
            </a>
          </div>
        )

      case 'divider':
        return (
          <hr
            style={baseStyles}
            className="mailchimp-divider-component"
          />
        )

      case 'spacer':
        return (
          <div
            style={baseStyles}
            className="mailchimp-spacer-component"
          />
        )

      case 'container':
        return (
          <div
            style={baseStyles}
            className="mailchimp-container-component"
          >
            {safeComponent.content.children?.map((child: TemplateComponent) => (
              <MailchimpComponentRenderer
                key={child.id}
                component={child}
                isSelected={false}
                onUpdate={() => {}}
                onDelete={() => {}}
                templateSettings={templateSettings}
              />
            )) || (
              <div className="text-gray-400 text-center py-8">
                Drop components here
              </div>
            )}
          </div>
        )

      case 'columns':
        const columnCount = safeComponent.content.columns || 2
        const columnContent = safeComponent.content.columnContent || []

        return (
          <div
            style={{
              ...baseStyles,
              display: 'flex',
              gap: component.styles?.gap || '20px'
            }}
            className="mailchimp-columns-component"
          >
            {Array.from({ length: columnCount }, (_, index) => (
              <div
                key={index}
                style={{
                  flex: 1,
                  minHeight: '100px',
                  padding: '16px',
                  border: '1px dashed #e1e5e9',
                  borderRadius: '4px'
                }}
              >
                <div dangerouslySetInnerHTML={{
                  __html: columnContent[index] || `Column ${index + 1} content`
                }} />
              </div>
            ))}
          </div>
        )

      case 'social':
        const platforms = safeComponent.content.platforms || []
        
        return (
          <div
            style={baseStyles}
            className="mailchimp-social-component"
          >
            {platforms.map((platform: any, index: number) => (
              <a
                key={index}
                href={platform.url || '#'}
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  display: 'inline-block',
                  margin: '0 8px',
                  padding: '8px',
                  backgroundColor: getSocialColor(platform.name),
                  color: 'white',
                  borderRadius: '4px',
                  textDecoration: 'none'
                }}
              >
                {getSocialIcon(platform.name)}
              </a>
            ))}
          </div>
        )

      case 'video':
        return (
          <div style={baseStyles} className="mailchimp-video-component">
            {safeComponent.content.src ? (
              <video
                controls
                style={{ width: '100%', height: 'auto' }}
                poster={safeComponent.content.thumbnail}
              >
                <source src={safeComponent.content.src} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            ) : (
              <div
                style={{
                  width: '100%',
                  height: '300px',
                  backgroundColor: '#f0f0f0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: '4px'
                }}
              >
                <div className="text-center">
                  <div className="text-4xl mb-2">▶️</div>
                  <p className="text-gray-600">{safeComponent.content.title || 'Video Title'}</p>
                </div>
              </div>
            )}
          </div>
        )

      case 'product':
        return (
          <div style={baseStyles} className="mailchimp-product-component">
            <div style={{ textAlign: 'center' }}>
              <img
                src={safeComponent.content.image || 'https://via.placeholder.com/200x200/f0f0f0/666666?text=Product'}
                alt={safeComponent.content.name || 'Product'}
                style={{
                  width: '200px',
                  height: '200px',
                  objectFit: 'cover',
                  borderRadius: '8px',
                  marginBottom: '16px'
                }}
              />
              <h3 style={{ margin: '0 0 8px 0', fontSize: '18px', fontWeight: 'bold' }}>
                {safeComponent.content.name || 'Product Name'}
              </h3>
              <p style={{ margin: '0 0 8px 0', fontSize: '20px', color: '#007C89', fontWeight: 'bold' }}>
                {safeComponent.content.price || '$99.99'}
              </p>
              <p style={{ margin: '0 0 16px 0', color: '#666' }}>
                {safeComponent.content.description || 'Product description goes here...'}
              </p>
              <a
                href={safeComponent.content.buttonLink || '#'}
                style={{
                  display: 'inline-block',
                  padding: '12px 24px',
                  backgroundColor: '#007C89',
                  color: 'white',
                  textDecoration: 'none',
                  borderRadius: '4px',
                  fontWeight: 'bold'
                }}
              >
                {safeComponent.content.buttonText || 'Shop Now'}
              </a>
            </div>
          </div>
        )

      case 'testimonial':
        return (
          <div style={baseStyles} className="mailchimp-testimonial-component">
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '16px' }}>
              <img
                src={safeComponent.content.avatar || 'https://via.placeholder.com/60x60/f0f0f0/666666?text=JD'}
                alt={safeComponent.content.author || 'Author'}
                style={{
                  width: '60px',
                  height: '60px',
                  borderRadius: '50%',
                  objectFit: 'cover'
                }}
              />
              <div style={{ flex: 1 }}>
                <blockquote style={{
                  margin: '0 0 12px 0',
                  fontSize: '16px',
                  fontStyle: 'italic',
                  color: '#333'
                }}>
                  "{safeComponent.content.quote || 'This is an amazing product! I highly recommend it.'}"
                </blockquote>
                <div>
                  <div style={{ fontWeight: 'bold', color: '#333' }}>
                    {safeComponent.content.author || 'John Doe'}
                  </div>
                  <div style={{ fontSize: '14px', color: '#666' }}>
                    {safeComponent.content.title || 'CEO, Company'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )

      // Handle legacy component types
      case 'header':
      case 'footer':
      case 'product-card':
      case 'social-media':
        return (
          <div
            style={{
              ...baseStyles,
              padding: '20px',
              border: '2px dashed #e1e5e9',
              textAlign: 'center',
              color: '#666',
              backgroundColor: '#f9f9f9'
            }}
          >
            Legacy component type: {safeComponent.type}
            <br />
            <small>Please update to a supported component type</small>
          </div>
        )

      default:
        return (
          <div
            style={{
              ...baseStyles,
              padding: '20px',
              border: '2px dashed #ccc',
              textAlign: 'center',
              color: '#666'
            }}
          >
            Unknown component type: {safeComponent.type}
          </div>
        )
    }
  }

  return (
    <div className="mailchimp-component-wrapper">
      {renderComponent()}
    </div>
  )
}

// Helper functions
const getSocialColor = (platform: string): string => {
  const colors: Record<string, string> = {
    facebook: '#1877F2',
    twitter: '#1DA1F2',
    instagram: '#E4405F',
    linkedin: '#0A66C2',
    youtube: '#FF0000',
    tiktok: '#000000'
  }
  return colors[platform.toLowerCase()] || '#666666'
}

const getSocialIcon = (platform: string): string => {
  const icons: Record<string, string> = {
    facebook: 'f',
    twitter: '🐦',
    instagram: '📷',
    linkedin: '💼',
    youtube: '📺',
    tiktok: '🎵'
  }
  return icons[platform.toLowerCase()] || '🔗'
}

export default MailchimpComponentRenderer
