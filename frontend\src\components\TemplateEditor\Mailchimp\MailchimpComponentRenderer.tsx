import React from 'react'
import { TemplateComponent, TemplateSettings } from '../../../types/templateEditor'

interface MailchimpComponentRendererProps {
  component: TemplateComponent
  isSelected: boolean
  onUpdate: (updates: Partial<TemplateComponent>) => void
  onDelete: () => void
  templateSettings: TemplateSettings
}

export const MailchimpComponentRenderer: React.FC<MailchimpComponentRendererProps> = ({
  component,
  isSelected,
  onUpdate,
  onDelete,
  templateSettings
}) => {
  const renderComponent = () => {
    const baseStyles = {
      ...component.styles,
      outline: isSelected ? '2px solid #3B82F6' : 'none',
      outlineOffset: isSelected ? '2px' : '0'
    }

    switch (component.type) {
      case 'text':
        return (
          <div
            style={baseStyles}
            className="mailchimp-text-component"
            dangerouslySetInnerHTML={{ __html: component.content.text || 'Your text here...' }}
          />
        )

      case 'heading':
        const HeadingTag = component.content.level || 'h2'
        return React.createElement(
          HeadingTag,
          {
            style: baseStyles,
            className: 'mailchimp-heading-component'
          },
          component.content.text || 'Your Heading'
        )

      case 'image':
        return (
          <div style={{ ...baseStyles, textAlign: 'center' }}>
            {component.content.link ? (
              <a href={component.content.link} target="_blank" rel="noopener noreferrer">
                <img
                  src={component.content.src || 'https://via.placeholder.com/600x300/f0f0f0/666666?text=Image'}
                  alt={component.content.alt || 'Image'}
                  style={{
                    maxWidth: '100%',
                    height: 'auto',
                    display: 'block',
                    margin: '0 auto'
                  }}
                  className="mailchimp-image-component"
                />
              </a>
            ) : (
              <img
                src={component.content.src || 'https://via.placeholder.com/600x300/f0f0f0/666666?text=Image'}
                alt={component.content.alt || 'Image'}
                style={{
                  maxWidth: '100%',
                  height: 'auto',
                  display: 'block',
                  margin: '0 auto'
                }}
                className="mailchimp-image-component"
              />
            )}
          </div>
        )

      case 'button':
        return (
          <div style={{ textAlign: 'center', padding: '16px' }}>
            <a
              href={component.content.href || '#'}
              target={component.content.target || '_blank'}
              style={baseStyles}
              className="mailchimp-button-component"
            >
              {component.content.text || 'Click Here'}
            </a>
          </div>
        )

      case 'divider':
        return (
          <hr
            style={baseStyles}
            className="mailchimp-divider-component"
          />
        )

      case 'spacer':
        return (
          <div
            style={baseStyles}
            className="mailchimp-spacer-component"
          />
        )

      case 'container':
        return (
          <div
            style={baseStyles}
            className="mailchimp-container-component"
          >
            {component.content.children?.map((child: TemplateComponent) => (
              <MailchimpComponentRenderer
                key={child.id}
                component={child}
                isSelected={false}
                onUpdate={() => {}}
                onDelete={() => {}}
                templateSettings={templateSettings}
              />
            )) || (
              <div className="text-gray-400 text-center py-8">
                Drop components here
              </div>
            )}
          </div>
        )

      case 'columns':
        const columnCount = component.content.columns || 2
        const columnContent = component.content.columnContent || []
        
        return (
          <div
            style={{
              ...baseStyles,
              display: 'flex',
              gap: component.styles.gap || '20px'
            }}
            className="mailchimp-columns-component"
          >
            {Array.from({ length: columnCount }, (_, index) => (
              <div
                key={index}
                style={{
                  flex: 1,
                  minHeight: '100px',
                  padding: '16px',
                  border: '1px dashed #e1e5e9',
                  borderRadius: '4px'
                }}
              >
                <div dangerouslySetInnerHTML={{ 
                  __html: columnContent[index] || `Column ${index + 1} content` 
                }} />
              </div>
            ))}
          </div>
        )

      case 'social':
        const platforms = component.content.platforms || []
        
        return (
          <div
            style={baseStyles}
            className="mailchimp-social-component"
          >
            {platforms.map((platform: any, index: number) => (
              <a
                key={index}
                href={platform.url || '#'}
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  display: 'inline-block',
                  margin: '0 8px',
                  padding: '8px',
                  backgroundColor: getSocialColor(platform.name),
                  color: 'white',
                  borderRadius: '4px',
                  textDecoration: 'none'
                }}
              >
                {getSocialIcon(platform.name)}
              </a>
            ))}
          </div>
        )

      case 'video':
        return (
          <div style={baseStyles} className="mailchimp-video-component">
            {component.content.src ? (
              <video
                controls
                style={{ width: '100%', height: 'auto' }}
                poster={component.content.thumbnail}
              >
                <source src={component.content.src} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            ) : (
              <div
                style={{
                  width: '100%',
                  height: '300px',
                  backgroundColor: '#f0f0f0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: '4px'
                }}
              >
                <div className="text-center">
                  <div className="text-4xl mb-2">▶️</div>
                  <p className="text-gray-600">{component.content.title || 'Video Title'}</p>
                </div>
              </div>
            )}
          </div>
        )

      case 'product':
        return (
          <div style={baseStyles} className="mailchimp-product-component">
            <div style={{ textAlign: 'center' }}>
              <img
                src={component.content.image || 'https://via.placeholder.com/200x200/f0f0f0/666666?text=Product'}
                alt={component.content.name || 'Product'}
                style={{
                  width: '200px',
                  height: '200px',
                  objectFit: 'cover',
                  borderRadius: '8px',
                  marginBottom: '16px'
                }}
              />
              <h3 style={{ margin: '0 0 8px 0', fontSize: '18px', fontWeight: 'bold' }}>
                {component.content.name || 'Product Name'}
              </h3>
              <p style={{ margin: '0 0 8px 0', fontSize: '20px', color: '#007C89', fontWeight: 'bold' }}>
                {component.content.price || '$99.99'}
              </p>
              <p style={{ margin: '0 0 16px 0', color: '#666' }}>
                {component.content.description || 'Product description goes here...'}
              </p>
              <a
                href={component.content.buttonLink || '#'}
                style={{
                  display: 'inline-block',
                  padding: '12px 24px',
                  backgroundColor: '#007C89',
                  color: 'white',
                  textDecoration: 'none',
                  borderRadius: '4px',
                  fontWeight: 'bold'
                }}
              >
                {component.content.buttonText || 'Shop Now'}
              </a>
            </div>
          </div>
        )

      case 'testimonial':
        return (
          <div style={baseStyles} className="mailchimp-testimonial-component">
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '16px' }}>
              <img
                src={component.content.avatar || 'https://via.placeholder.com/60x60/f0f0f0/666666?text=JD'}
                alt={component.content.author || 'Author'}
                style={{
                  width: '60px',
                  height: '60px',
                  borderRadius: '50%',
                  objectFit: 'cover'
                }}
              />
              <div style={{ flex: 1 }}>
                <blockquote style={{ 
                  margin: '0 0 12px 0', 
                  fontSize: '16px', 
                  fontStyle: 'italic',
                  color: '#333'
                }}>
                  "{component.content.quote || 'This is an amazing product! I highly recommend it.'}"
                </blockquote>
                <div>
                  <div style={{ fontWeight: 'bold', color: '#333' }}>
                    {component.content.author || 'John Doe'}
                  </div>
                  <div style={{ fontSize: '14px', color: '#666' }}>
                    {component.content.title || 'CEO, Company'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )

      default:
        return (
          <div
            style={{
              ...baseStyles,
              padding: '20px',
              border: '2px dashed #ccc',
              textAlign: 'center',
              color: '#666'
            }}
          >
            Unknown component type: {component.type}
          </div>
        )
    }
  }

  return (
    <div className="mailchimp-component-wrapper">
      {renderComponent()}
    </div>
  )
}

// Helper functions
const getSocialColor = (platform: string): string => {
  const colors: Record<string, string> = {
    facebook: '#1877F2',
    twitter: '#1DA1F2',
    instagram: '#E4405F',
    linkedin: '#0A66C2',
    youtube: '#FF0000',
    tiktok: '#000000'
  }
  return colors[platform.toLowerCase()] || '#666666'
}

const getSocialIcon = (platform: string): string => {
  const icons: Record<string, string> = {
    facebook: 'f',
    twitter: '🐦',
    instagram: '📷',
    linkedin: '💼',
    youtube: '📺',
    tiktok: '🎵'
  }
  return icons[platform.toLowerCase()] || '🔗'
}

export default MailchimpComponentRenderer
