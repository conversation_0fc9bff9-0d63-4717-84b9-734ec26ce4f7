import React from 'react'
import { Filter, Calendar, TrendingUp, ArrowUpDown } from 'lucide-react'

interface Filters {
  status: string
  type: string
  dateRange: string
  performance: string
}

interface MailchimpCampaignFiltersProps {
  filters: Filters
  onFiltersChange: (filters: Filters) => void
  sortBy: 'name' | 'created' | 'sent' | 'performance'
  sortOrder: 'asc' | 'desc'
  onSortChange: (field: 'name' | 'created' | 'sent' | 'performance', order: 'asc' | 'desc') => void
}

export const MailchimpCampaignFilters: React.FC<MailchimpCampaignFiltersProps> = ({
  filters,
  onFiltersChange,
  sortBy,
  sortOrder,
  onSortChange
}) => {
  const updateFilter = (key: keyof Filters, value: string) => {
    onFiltersChange({ ...filters, [key]: value })
  }

  const handleSort = (field: 'name' | 'created' | 'sent' | 'performance') => {
    const newOrder = sortBy === field && sortOrder === 'asc' ? 'desc' : 'asc'
    onSortChange(field, newOrder)
  }

  return (
    <div className="space-y-4">
      {/* Advanced Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Date Range
          </label>
          <select
            value={filters.dateRange}
            onChange={(e) => updateFilter('dateRange', e.target.value)}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="7days">Last 7 days</option>
            <option value="30days">Last 30 days</option>
            <option value="90days">Last 90 days</option>
            <option value="6months">Last 6 months</option>
            <option value="1year">Last year</option>
            <option value="all">All time</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Performance
          </label>
          <select
            value={filters.performance}
            onChange={(e) => updateFilter('performance', e.target.value)}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Performance</option>
            <option value="high">High Performers (>25% open rate)</option>
            <option value="medium">Medium Performers (10-25% open rate)</option>
            <option value="low">Low Performers (<10% open rate)</option>
            <option value="no-data">No Performance Data</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Sort By
          </label>
          <div className="flex space-x-2">
            <select
              value={sortBy}
              onChange={(e) => onSortChange(e.target.value as any, sortOrder)}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="created">Date Created</option>
              <option value="sent">Date Sent</option>
              <option value="name">Campaign Name</option>
              <option value="performance">Performance</option>
            </select>
            <button
              onClick={() => handleSort(sortBy)}
              className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
            >
              <ArrowUpDown className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Quick Filters
          </label>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => updateFilter('status', 'draft')}
              className={`px-3 py-1 text-xs rounded-full transition-colors ${
                filters.status === 'draft'
                  ? 'bg-gray-200 text-gray-800'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              Drafts
            </button>
            <button
              onClick={() => updateFilter('status', 'sent')}
              className={`px-3 py-1 text-xs rounded-full transition-colors ${
                filters.status === 'sent'
                  ? 'bg-green-200 text-green-800'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              Sent
            </button>
            <button
              onClick={() => updateFilter('type', 'ab_test')}
              className={`px-3 py-1 text-xs rounded-full transition-colors ${
                filters.type === 'ab_test'
                  ? 'bg-blue-200 text-blue-800'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              A/B Tests
            </button>
          </div>
        </div>
      </div>

      {/* Active Filters Display */}
      {(filters.status !== 'all' || filters.type !== 'all' || filters.performance !== 'all' || filters.dateRange !== '30days') && (
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">Active filters:</span>
          <div className="flex flex-wrap gap-2">
            {filters.status !== 'all' && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                Status: {filters.status}
                <button
                  onClick={() => updateFilter('status', 'all')}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </span>
            )}
            {filters.type !== 'all' && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                Type: {filters.type.replace('_', ' ')}
                <button
                  onClick={() => updateFilter('type', 'all')}
                  className="ml-1 text-green-600 hover:text-green-800"
                >
                  ×
                </button>
              </span>
            )}
            {filters.performance !== 'all' && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
                Performance: {filters.performance}
                <button
                  onClick={() => updateFilter('performance', 'all')}
                  className="ml-1 text-purple-600 hover:text-purple-800"
                >
                  ×
                </button>
              </span>
            )}
            {filters.dateRange !== '30days' && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
                Date: {filters.dateRange}
                <button
                  onClick={() => updateFilter('dateRange', '30days')}
                  className="ml-1 text-yellow-600 hover:text-yellow-800"
                >
                  ×
                </button>
              </span>
            )}
            <button
              onClick={() => onFiltersChange({
                status: 'all',
                type: 'all',
                dateRange: '30days',
                performance: 'all'
              })}
              className="text-xs text-gray-500 hover:text-gray-700 underline"
            >
              Clear all
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default MailchimpCampaignFilters
