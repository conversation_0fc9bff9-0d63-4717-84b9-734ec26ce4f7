import React, { useState, useEffect } from 'react'
import { Users, Search, Filter, Plus, Target, Tag, Info, CheckCircle } from 'lucide-react'
import { api } from '../../../services/api'

interface ContactList {
  id: number
  name: string
  description?: string
  contactCount: number
  created_at: string
  isDefault?: boolean
}

interface Segment {
  id: number
  name: string
  description?: string
  contactCount: number
  conditions: any[]
  listId: number
}

interface CampaignData {
  recipients: {
    lists: number[]
    segments: number[]
    tags: string[]
    totalCount: number
  }
}

interface MailchimpRecipientSelectorProps {
  campaignData: CampaignData
  onUpdate: (updates: Partial<CampaignData>) => void
  onNext: () => void
  onPrevious: () => void
  canProceed: boolean
  isLastStep: boolean
  loading: boolean
}

export const MailchimpRecipientSelector: React.FC<MailchimpRecipientSelectorProps> = ({
  campaignData,
  onUpdate,
  canProceed,
  loading
}) => {
  const [contactLists, setContactLists] = useState<ContactList[]>([])
  const [segments, setSegments] = useState<Segment[]>([])
  const [availableTags, setAvailableTags] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState<'lists' | 'segments' | 'tags'>('lists')
  const [loadingData, setLoadingData] = useState(true)

  useEffect(() => {
    loadRecipientData()
  }, [])

  useEffect(() => {
    calculateTotalRecipients()
  }, [campaignData.recipients.lists, campaignData.recipients.segments, campaignData.recipients.tags])

  const loadRecipientData = async () => {
    try {
      setLoadingData(true)
      
      // Load contact lists
      const listsResponse = await api.get<ContactList[]>('/contact-lists')
      setContactLists(listsResponse || [])
      
      // Load segments
      const segmentsResponse = await api.get<Segment[]>('/segments')
      setSegments(segmentsResponse || [])
      
      // Load available tags
      const tagsResponse = await api.get<string[]>('/contacts/tags')
      setAvailableTags(tagsResponse || [])
      
    } catch (error) {
      console.error('Failed to load recipient data:', error)
    } finally {
      setLoadingData(false)
    }
  }

  const calculateTotalRecipients = async () => {
    try {
      const response = await api.post<{ totalCount: number }>('/campaigns/calculate-recipients', {
        lists: campaignData.recipients.lists,
        segments: campaignData.recipients.segments,
        tags: campaignData.recipients.tags
      })
      
      onUpdate({
        recipients: {
          ...campaignData.recipients,
          totalCount: response?.totalCount || 0
        }
      })
    } catch (error) {
      console.error('Failed to calculate recipients:', error)
    }
  }

  const toggleList = (listId: number) => {
    const currentLists = campaignData.recipients.lists
    const newLists = currentLists.includes(listId)
      ? currentLists.filter(id => id !== listId)
      : [...currentLists, listId]
    
    onUpdate({
      recipients: {
        ...campaignData.recipients,
        lists: newLists
      }
    })
  }

  const toggleSegment = (segmentId: number) => {
    const currentSegments = campaignData.recipients.segments
    const newSegments = currentSegments.includes(segmentId)
      ? currentSegments.filter(id => id !== segmentId)
      : [...currentSegments, segmentId]
    
    onUpdate({
      recipients: {
        ...campaignData.recipients,
        segments: newSegments
      }
    })
  }

  const toggleTag = (tag: string) => {
    const currentTags = campaignData.recipients.tags
    const newTags = currentTags.includes(tag)
      ? currentTags.filter(t => t !== tag)
      : [...currentTags, tag]
    
    onUpdate({
      recipients: {
        ...campaignData.recipients,
        tags: newTags
      }
    })
  }

  const filteredLists = contactLists.filter(list =>
    list.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredSegments = segments.filter(segment =>
    segment.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredTags = availableTags.filter(tag =>
    tag.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const tabs = [
    { id: 'lists', name: 'Lists', icon: Users, count: contactLists.length },
    { id: 'segments', name: 'Segments', icon: Target, count: segments.length },
    { id: 'tags', name: 'Tags', icon: Tag, count: availableTags.length }
  ]

  if (loadingData) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Choose Recipients</h2>
        <p className="text-gray-600">
          Select the lists, segments, or tags to define your campaign audience
        </p>
      </div>

      {/* Recipient Count Summary */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-full">
              <Users className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium text-blue-900">
                {campaignData.recipients.totalCount.toLocaleString()} recipients selected
              </h3>
              <p className="text-sm text-blue-700">
                {campaignData.recipients.lists.length} lists, {campaignData.recipients.segments.length} segments, {campaignData.recipients.tags.length} tags
              </p>
            </div>
          </div>
          {canProceed && (
            <CheckCircle className="w-6 h-6 text-green-500" />
          )}
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <input
          type="text"
          placeholder="Search lists, segments, or tags..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {tabs.map(tab => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4 inline mr-2" />
                {tab.name}
                <span className="ml-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                  {tab.count}
                </span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Content */}
      <div className="space-y-4">
        {activeTab === 'lists' && (
          <div className="space-y-3">
            {filteredLists.length === 0 ? (
              <div className="text-center py-8">
                <Users className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No lists found</h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm ? 'Try adjusting your search' : 'Create your first contact list to get started'}
                </p>
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  <Plus className="w-4 h-4 mr-2 inline" />
                  Create List
                </button>
              </div>
            ) : (
              filteredLists.map(list => (
                <div
                  key={list.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    campaignData.recipients.lists.includes(list.id)
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => toggleList(list.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={campaignData.recipients.lists.includes(list.id)}
                        onChange={() => toggleList(list.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <div>
                        <h3 className="font-medium text-gray-900">
                          {list.name}
                          {list.isDefault && (
                            <span className="ml-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                              Default
                            </span>
                          )}
                        </h3>
                        {list.description && (
                          <p className="text-sm text-gray-600 mt-1">{list.description}</p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">
                        {list.contactCount.toLocaleString()}
                      </p>
                      <p className="text-sm text-gray-600">contacts</p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {activeTab === 'segments' && (
          <div className="space-y-3">
            {filteredSegments.length === 0 ? (
              <div className="text-center py-8">
                <Target className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No segments found</h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm ? 'Try adjusting your search' : 'Create segments to target specific groups'}
                </p>
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  <Plus className="w-4 h-4 mr-2 inline" />
                  Create Segment
                </button>
              </div>
            ) : (
              filteredSegments.map(segment => (
                <div
                  key={segment.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    campaignData.recipients.segments.includes(segment.id)
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => toggleSegment(segment.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={campaignData.recipients.segments.includes(segment.id)}
                        onChange={() => toggleSegment(segment.id)}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <div>
                        <h3 className="font-medium text-gray-900">{segment.name}</h3>
                        {segment.description && (
                          <p className="text-sm text-gray-600 mt-1">{segment.description}</p>
                        )}
                        <p className="text-xs text-gray-500 mt-1">
                          {segment.conditions.length} condition{segment.conditions.length !== 1 ? 's' : ''}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">
                        {segment.contactCount.toLocaleString()}
                      </p>
                      <p className="text-sm text-gray-600">contacts</p>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {activeTab === 'tags' && (
          <div className="space-y-3">
            {filteredTags.length === 0 ? (
              <div className="text-center py-8">
                <Tag className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No tags found</h3>
                <p className="text-gray-600">
                  {searchTerm ? 'Try adjusting your search' : 'No tags are available'}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {filteredTags.map(tag => (
                  <button
                    key={tag}
                    onClick={() => toggleTag(tag)}
                    className={`p-3 border rounded-lg text-left transition-all ${
                      campaignData.recipients.tags.includes(tag)
                        ? 'border-purple-500 bg-purple-50 text-purple-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <Tag className="w-4 h-4" />
                      <span className="font-medium truncate">{tag}</span>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Validation Message */}
      {!canProceed && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <Info className="w-5 h-5 text-yellow-400 mr-3 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800">
                Select at least one recipient group
              </h3>
              <p className="mt-1 text-sm text-yellow-700">
                Choose from lists, segments, or tags to define your campaign audience.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default MailchimpRecipientSelector
